<?php
class AHP {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Mendapatkan skala perbandingan AHP
     */
    public function getSkalaPerbandingan() {
        return [
            1 => 'Sama penting',
            2 => 'Sedikit lebih penting',
            3 => 'Lebih penting',
            4 => 'Sangat lebih penting',
            5 => 'Mutlak lebih penting',
            6 => 'Diantara 5 dan 7',
            7 => 'Sangat mutlak lebih penting',
            8 => 'Diantara 7 dan 9',
            9 => 'Ekstrim lebih penting'
        ];
    }
    
    /**
     * Membuat matriks perbandingan berpasangan
     */
    public function createPairwiseMatrix() {
        // Ambil semua kriteria
        $stmt = $this->db->query("SELECT * FROM kriteria ORDER BY kode");
        $kriteria = $stmt->fetchAll();
        
        $n = count($kriteria);
        $matrix = [];
        
        // Inisialisasi matriks dengan nilai 1 pada diagonal
        for ($i = 0; $i < $n; $i++) {
            for ($j = 0; $j < $n; $j++) {
                if ($i == $j) {
                    $matrix[$i][$j] = 1;
                } else {
                    $matrix[$i][$j] = 0;
                }
            }
        }
        
        // Ambil nilai perbandingan yang sudah ada
        $stmt = $this->db->query("
            SELECT pk.*, k1.kode as kode1, k2.kode as kode2 
            FROM perbandingan_kriteria pk
            JOIN kriteria k1 ON pk.kriteria1_id = k1.id
            JOIN kriteria k2 ON pk.kriteria2_id = k2.id
        ");
        $perbandingan = $stmt->fetchAll();
        
        // Isi matriks dengan nilai perbandingan
        foreach ($perbandingan as $p) {
            $i = array_search($p['kriteria1_id'], array_column($kriteria, 'id'));
            $j = array_search($p['kriteria2_id'], array_column($kriteria, 'id'));
            
            if ($i !== false && $j !== false) {
                $matrix[$i][$j] = $p['nilai'];
                $matrix[$j][$i] = 1 / $p['nilai']; // Reciprocal
            }
        }
        
        return [
            'kriteria' => $kriteria,
            'matrix' => $matrix
        ];
    }
    
    /**
     * Menghitung bobot kriteria menggunakan metode Eigenvector
     */
    public function calculateWeights() {
        $data = $this->createPairwiseMatrix();
        $matrix = $data['matrix'];
        $kriteria = $data['kriteria'];
        $n = count($kriteria);
        
        if ($n == 0) return [];
        
        // Normalisasi matriks
        $normalizedMatrix = [];
        $columnSums = [];
        
        // Hitung jumlah setiap kolom
        for ($j = 0; $j < $n; $j++) {
            $sum = 0;
            for ($i = 0; $i < $n; $i++) {
                $sum += $matrix[$i][$j];
            }
            $columnSums[$j] = $sum;
        }
        
        // Normalisasi
        for ($i = 0; $i < $n; $i++) {
            for ($j = 0; $j < $n; $j++) {
                $normalizedMatrix[$i][$j] = $columnSums[$j] != 0 ? $matrix[$i][$j] / $columnSums[$j] : 0;
            }
        }
        
        // Hitung rata-rata setiap baris (bobot)
        $weights = [];
        for ($i = 0; $i < $n; $i++) {
            $sum = 0;
            for ($j = 0; $j < $n; $j++) {
                $sum += $normalizedMatrix[$i][$j];
            }
            $weights[$i] = $sum / $n;
        }
        
        // Update bobot di database
        for ($i = 0; $i < $n; $i++) {
            $stmt = $this->db->prepare("UPDATE kriteria SET bobot = ? WHERE id = ?");
            $stmt->execute([$weights[$i], $kriteria[$i]['id']]);
        }
        
        return [
            'kriteria' => $kriteria,
            'weights' => $weights,
            'matrix' => $matrix,
            'normalizedMatrix' => $normalizedMatrix,
            'cr' => $this->calculateConsistencyRatio($matrix, $weights)
        ];
    }
    
    /**
     * Menghitung Consistency Ratio (CR)
     */
    public function calculateConsistencyRatio($matrix, $weights) {
        $n = count($weights);
        
        if ($n <= 2) return 0; // CR tidak berlaku untuk matriks 2x2 atau kurang
        
        // Random Index (RI) values
        $ri = [0, 0, 0.58, 0.90, 1.12, 1.24, 1.32, 1.41, 1.45, 1.49];
        
        // Hitung lambda max
        $lambdaMax = 0;
        for ($i = 0; $i < $n; $i++) {
            $sum = 0;
            for ($j = 0; $j < $n; $j++) {
                $sum += $matrix[$i][$j] * $weights[$j];
            }
            $lambdaMax += $sum / $weights[$i];
        }
        $lambdaMax = $lambdaMax / $n;
        
        // Hitung CI (Consistency Index)
        $ci = ($lambdaMax - $n) / ($n - 1);
        
        // Hitung CR (Consistency Ratio)
        $cr = $ci / $ri[$n - 1];
        
        return $cr;
    }
    
    /**
     * Simpan perbandingan kriteria
     */
    public function saveComparison($kriteria1_id, $kriteria2_id, $nilai) {
        try {
            // Hapus perbandingan yang sudah ada
            $stmt = $this->db->prepare("DELETE FROM perbandingan_kriteria WHERE kriteria1_id = ? AND kriteria2_id = ?");
            $stmt->execute([$kriteria1_id, $kriteria2_id]);
            
            // Simpan perbandingan baru
            $stmt = $this->db->prepare("INSERT INTO perbandingan_kriteria (kriteria1_id, kriteria2_id, nilai) VALUES (?, ?, ?)");
            $stmt->execute([$kriteria1_id, $kriteria2_id, $nilai]);
            
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Reset semua perbandingan
     */
    public function resetComparisons() {
        try {
            $this->db->exec("DELETE FROM perbandingan_kriteria");
            $this->db->exec("UPDATE kriteria SET bobot = 0");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Cek apakah perbandingan sudah lengkap
     */
    public function isComparisonComplete() {
        $stmt = $this->db->query("SELECT COUNT(*) as total FROM kriteria");
        $totalKriteria = $stmt->fetch()['total'];
        
        $stmt = $this->db->query("SELECT COUNT(*) as total FROM perbandingan_kriteria");
        $totalPerbandingan = $stmt->fetch()['total'];
        
        $expectedComparisons = ($totalKriteria * ($totalKriteria - 1)) / 2;
        
        return $totalPerbandingan >= $expectedComparisons;
    }
}
?>
