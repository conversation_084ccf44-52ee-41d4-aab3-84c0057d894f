<?php
// Konfigurasi Aplikasi
session_start();

// Base URL
define('BASE_URL', 'http://localhost/Aplikasi%20Pemilihan%20pupuk%20Organik/');

// Path
define('ROOT_PATH', dirname(__DIR__) . '/');

// Include database
require_once ROOT_PATH . 'config/database.php';

// Fungsi untuk redirect
function redirect($url) {
    header("Location: " . BASE_URL . $url);
    exit();
}

// Fungsi untuk cek login
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// Fungsi untuk cek admin
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

// Fungsi untuk require login
function requireLogin() {
    if (!isLoggedIn()) {
        redirect('login.php');
    }
}

// Fungsi untuk require admin
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        redirect('dashboard.php');
    }
}

// Fungsi untuk format angka
function formatNumber($number, $decimals = 6) {
    return number_format($number, $decimals, '.', '');
}

// Fungsi untuk sanitize input
function sanitize($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Fungsi untuk generate CSRF token
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Fungsi untuk verify CSRF token
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Set timezone
date_default_timezone_set('Asia/Jakarta');
?>
