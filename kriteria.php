<?php
require_once 'config/config.php';
requireLogin();

$page_title = 'Data Kriteria';
$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $db = getDB();
        
        if ($_POST['action'] === 'add') {
            $kode = sanitize($_POST['kode']);
            $nama = sanitize($_POST['nama']);
            $deskripsi = sanitize($_POST['deskripsi']);
            $jenis = sanitize($_POST['jenis']);
            
            if (empty($kode) || empty($nama)) {
                $error = 'Kode dan nama kriteria harus diisi!';
            } else {
                try {
                    // Cek kode unik
                    $stmt = $db->prepare("SELECT COUNT(*) FROM kriteria WHERE kode = ?");
                    $stmt->execute([$kode]);
                    
                    if ($stmt->fetchColumn() > 0) {
                        $error = 'Kode kriteria sudah digunakan!';
                    } else {
                        $stmt = $db->prepare("INSERT INTO kriteria (kode, nama, deskripsi, jenis) VALUES (?, ?, ?, ?)");
                        $stmt->execute([$kode, $nama, $deskripsi, $jenis]);
                        $success = 'Kriteria berhasil ditambahkan!';
                    }
                } catch (Exception $e) {
                    $error = 'Terjadi kesalahan: ' . $e->getMessage();
                }
            }
        }
        
        elseif ($_POST['action'] === 'edit') {
            $id = (int)$_POST['id'];
            $kode = sanitize($_POST['kode']);
            $nama = sanitize($_POST['nama']);
            $deskripsi = sanitize($_POST['deskripsi']);
            $jenis = sanitize($_POST['jenis']);
            
            if (empty($kode) || empty($nama)) {
                $error = 'Kode dan nama kriteria harus diisi!';
            } else {
                try {
                    // Cek kode unik (kecuali untuk kriteria yang sedang diedit)
                    $stmt = $db->prepare("SELECT COUNT(*) FROM kriteria WHERE kode = ? AND id != ?");
                    $stmt->execute([$kode, $id]);
                    
                    if ($stmt->fetchColumn() > 0) {
                        $error = 'Kode kriteria sudah digunakan!';
                    } else {
                        $stmt = $db->prepare("UPDATE kriteria SET kode = ?, nama = ?, deskripsi = ?, jenis = ? WHERE id = ?");
                        $stmt->execute([$kode, $nama, $deskripsi, $jenis, $id]);
                        $success = 'Kriteria berhasil diupdate!';
                    }
                } catch (Exception $e) {
                    $error = 'Terjadi kesalahan: ' . $e->getMessage();
                }
            }
        }
    }
}

// Handle delete
if (isset($_GET['delete'])) {
    $id = (int)$_GET['delete'];
    
    try {
        $db = getDB();
        
        // Cek apakah kriteria digunakan dalam perbandingan
        $stmt = $db->prepare("SELECT COUNT(*) FROM perbandingan_kriteria WHERE kriteria1_id = ? OR kriteria2_id = ?");
        $stmt->execute([$id, $id]);
        
        if ($stmt->fetchColumn() > 0) {
            $error = 'Kriteria tidak dapat dihapus karena sudah digunakan dalam perbandingan!';
        } else {
            $stmt = $db->prepare("DELETE FROM kriteria WHERE id = ?");
            $stmt->execute([$id]);
            $success = 'Kriteria berhasil dihapus!';
        }
    } catch (Exception $e) {
        $error = 'Terjadi kesalahan: ' . $e->getMessage();
    }
}

// Get all kriteria
try {
    $db = getDB();
    $stmt = $db->query("SELECT * FROM kriteria ORDER BY kode");
    $kriteria = $stmt->fetchAll();
} catch (Exception $e) {
    $error = 'Terjadi kesalahan dalam mengambil data kriteria.';
    $kriteria = [];
}

include 'includes/header.php';
?>

<?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?= $success ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i><?= $error ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Data Kriteria</h5>
        <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addKriteriaModal">
            <i class="fas fa-plus me-2"></i>Tambah Kriteria
        </button>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped data-table">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Kode</th>
                        <th>Nama Kriteria</th>
                        <th>Jenis</th>
                        <th>Bobot</th>
                        <th>Deskripsi</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($kriteria as $index => $k): ?>
                    <tr>
                        <td><?= $index + 1 ?></td>
                        <td><span class="badge bg-primary"><?= htmlspecialchars($k['kode']) ?></span></td>
                        <td><?= htmlspecialchars($k['nama']) ?></td>
                        <td>
                            <span class="badge bg-<?= $k['jenis'] === 'benefit' ? 'success' : 'warning' ?>">
                                <?= ucfirst($k['jenis']) ?>
                            </span>
                        </td>
                        <td><?= formatNumber($k['bobot'], 4) ?></td>
                        <td><?= htmlspecialchars($k['deskripsi']) ?></td>
                        <td>
                            <button class="btn btn-sm btn-warning me-1" onclick="editKriteria(<?= htmlspecialchars(json_encode($k)) ?>)">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="confirmDelete('kriteria.php?delete=<?= $k['id'] ?>', 'Kriteria ini akan dihapus permanen!')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Kriteria Modal -->
<div class="modal fade" id="addKriteriaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Tambah Kriteria Baru</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="mb-3">
                        <label class="form-label">Kode Kriteria</label>
                        <input type="text" class="form-control" name="kode" placeholder="Contoh: C1" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Kriteria</label>
                        <input type="text" class="form-control" name="nama" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Jenis Kriteria</label>
                        <select class="form-select" name="jenis" required>
                            <option value="benefit">Benefit (Semakin tinggi semakin baik)</option>
                            <option value="cost">Cost (Semakin rendah semakin baik)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Deskripsi</label>
                        <textarea class="form-control" name="deskripsi" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Kriteria Modal -->
<div class="modal fade" id="editKriteriaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Kriteria</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label class="form-label">Kode Kriteria</label>
                        <input type="text" class="form-control" name="kode" id="edit_kode" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Kriteria</label>
                        <input type="text" class="form-control" name="nama" id="edit_nama" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Jenis Kriteria</label>
                        <select class="form-select" name="jenis" id="edit_jenis" required>
                            <option value="benefit">Benefit (Semakin tinggi semakin baik)</option>
                            <option value="cost">Cost (Semakin rendah semakin baik)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Deskripsi</label>
                        <textarea class="form-control" name="deskripsi" id="edit_deskripsi" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$extra_js = "
<script>
function editKriteria(kriteria) {
    document.getElementById('edit_id').value = kriteria.id;
    document.getElementById('edit_kode').value = kriteria.kode;
    document.getElementById('edit_nama').value = kriteria.nama;
    document.getElementById('edit_jenis').value = kriteria.jenis;
    document.getElementById('edit_deskripsi').value = kriteria.deskripsi;
    
    new bootstrap.Modal(document.getElementById('editKriteriaModal')).show();
}
</script>
";

include 'includes/footer.php';
?>
