<?php
require_once 'config/config.php';
requireLogin();

$page_title = 'Penilaian Alternatif';
$success = '';
$error = '';

$db = getDB();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'save_nilai') {
        $alternatif_id = (int)$_POST['alternatif_id'];
        $kriteria_id = (int)$_POST['kriteria_id'];
        $nilai = (float)$_POST['nilai'];
        
        if ($nilai < 0 || $nilai > 10) {
            $error = 'Nilai harus antara 0 dan 10!';
        } else {
            try {
                // Cek apakah nilai sudah ada
                $stmt = $db->prepare("SELECT COUNT(*) FROM nilai_alternatif WHERE alternatif_id = ? AND kriteria_id = ?");
                $stmt->execute([$alternatif_id, $kriteria_id]);
                
                if ($stmt->fetchColumn() > 0) {
                    // Update nilai yang sudah ada
                    $stmt = $db->prepare("UPDATE nilai_alternatif SET nilai = ? WHERE alternatif_id = ? AND kriteria_id = ?");
                    $stmt->execute([$nilai, $alternatif_id, $kriteria_id]);
                } else {
                    // Insert nilai baru
                    $stmt = $db->prepare("INSERT INTO nilai_alternatif (alternatif_id, kriteria_id, nilai) VALUES (?, ?, ?)");
                    $stmt->execute([$alternatif_id, $kriteria_id, $nilai]);
                }
                
                $success = 'Nilai berhasil disimpan!';
            } catch (Exception $e) {
                $error = 'Terjadi kesalahan: ' . $e->getMessage();
            }
        }
    }
}

// Ambil data kriteria dan alternatif
try {
    $stmt = $db->query("SELECT * FROM kriteria ORDER BY kode");
    $kriteria = $stmt->fetchAll();
    
    $stmt = $db->query("SELECT * FROM alternatif ORDER BY kode");
    $alternatif = $stmt->fetchAll();
    
    // Ambil nilai yang sudah ada
    $nilai_existing = [];
    $stmt = $db->query("SELECT * FROM nilai_alternatif");
    $nilai_data = $stmt->fetchAll();
    
    foreach ($nilai_data as $n) {
        $nilai_existing[$n['alternatif_id']][$n['kriteria_id']] = $n['nilai'];
    }
    
} catch (Exception $e) {
    $error = 'Terjadi kesalahan dalam mengambil data.';
    $kriteria = [];
    $alternatif = [];
    $nilai_existing = [];
}

include 'includes/header.php';
?>

<?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?= $success ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i><?= $error ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (empty($kriteria) || empty($alternatif)): ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        Data kriteria dan alternatif harus ada terlebih dahulu. 
        <a href="kriteria.php" class="alert-link">Kelola kriteria</a> dan 
        <a href="alternatif.php" class="alert-link">kelola alternatif</a>.
    </div>
<?php else: ?>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-star me-2"></i>Penilaian Alternatif terhadap Kriteria</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Petunjuk Penilaian:</strong><br>
            Berikan nilai untuk setiap alternatif pada setiap kriteria dengan skala 1-10, dimana:
            <ul class="mb-0 mt-2">
                <li><strong>1-2:</strong> Sangat Buruk</li>
                <li><strong>3-4:</strong> Buruk</li>
                <li><strong>5-6:</strong> Cukup</li>
                <li><strong>7-8:</strong> Baik</li>
                <li><strong>9-10:</strong> Sangat Baik</li>
            </ul>
        </div>
        
        <!-- Desktop Table View -->
        <div class="table-responsive d-none d-md-block">
            <table class="table table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th rowspan="2" class="align-middle">Alternatif</th>
                        <th colspan="<?= count($kriteria) ?>" class="text-center">Kriteria</th>
                    </tr>
                    <tr>
                        <?php foreach ($kriteria as $k): ?>
                            <th class="text-center" style="min-width: 120px;">
                                <div><?= $k['kode'] ?></div>
                                <small class="text-muted"><?= htmlspecialchars($k['nama']) ?></small>
                                <br>
                                <span class="badge bg-<?= $k['jenis'] === 'benefit' ? 'success' : 'warning' ?> mt-1">
                                    <?= ucfirst($k['jenis']) ?>
                                </span>
                            </th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($alternatif as $alt): ?>
                    <tr>
                        <td class="align-middle">
                            <strong><?= $alt['kode'] ?></strong><br>
                            <small class="text-muted"><?= htmlspecialchars($alt['nama']) ?></small>
                        </td>
                        <?php foreach ($kriteria as $k): ?>
                            <td class="text-center">
                                <?php
                                $current_value = isset($nilai_existing[$alt['id']][$k['id']]) ? $nilai_existing[$alt['id']][$k['id']] : '';
                                ?>
                                <form method="POST" class="nilai-form">
                                    <input type="hidden" name="action" value="save_nilai">
                                    <input type="hidden" name="alternatif_id" value="<?= $alt['id'] ?>">
                                    <input type="hidden" name="kriteria_id" value="<?= $k['id'] ?>">

                                    <div class="input-group input-group-sm">
                                        <input type="number"
                                               class="form-control text-center"
                                               name="nilai"
                                               value="<?= $current_value ?>"
                                               min="1"
                                               max="10"
                                               step="0.1"
                                               placeholder="1-10"
                                               onchange="this.form.submit()">
                                        <button type="submit" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-save"></i>
                                        </button>
                                    </div>
                                </form>

                                <?php if ($current_value): ?>
                                    <small class="text-success mt-1 d-block">
                                        <i class="fas fa-check"></i> Tersimpan
                                    </small>
                                <?php endif; ?>
                            </td>
                        <?php endforeach; ?>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Mobile Card View -->
        <div class="d-md-none">
            <?php foreach ($alternatif as $alt): ?>
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <span class="badge bg-success me-2"><?= $alt['kode'] ?></span>
                            <?= htmlspecialchars($alt['nama']) ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($kriteria as $k): ?>
                            <?php
                            $current_value = isset($nilai_existing[$alt['id']][$k['id']]) ? $nilai_existing[$alt['id']][$k['id']] : '';
                            ?>
                            <div class="row mb-3 align-items-center">
                                <div class="col-5">
                                    <strong><?= $k['kode'] ?></strong><br>
                                    <small class="text-muted"><?= htmlspecialchars($k['nama']) ?></small><br>
                                    <span class="badge bg-<?= $k['jenis'] === 'benefit' ? 'success' : 'warning' ?> badge-sm">
                                        <?= ucfirst($k['jenis']) ?>
                                    </span>
                                </div>
                                <div class="col-7">
                                    <form method="POST" class="nilai-form">
                                        <input type="hidden" name="action" value="save_nilai">
                                        <input type="hidden" name="alternatif_id" value="<?= $alt['id'] ?>">
                                        <input type="hidden" name="kriteria_id" value="<?= $k['id'] ?>">

                                        <div class="input-group input-group-sm">
                                            <input type="number"
                                                   class="form-control text-center"
                                                   name="nilai"
                                                   value="<?= $current_value ?>"
                                                   min="1"
                                                   max="10"
                                                   step="0.1"
                                                   placeholder="1-10"
                                                   style="font-size: 16px;"
                                                   onchange="this.form.submit()">
                                            <button type="submit" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-save"></i>
                                            </button>
                                        </div>

                                        <?php if ($current_value): ?>
                                            <small class="text-success mt-1 d-block">
                                                <i class="fas fa-check"></i> Tersimpan
                                            </small>
                                        <?php endif; ?>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Progress Penilaian -->
        <div class="mt-4">
            <?php
            $total_cells = count($alternatif) * count($kriteria);
            $filled_cells = 0;
            foreach ($alternatif as $alt) {
                foreach ($kriteria as $k) {
                    if (isset($nilai_existing[$alt['id']][$k['id']]) && $nilai_existing[$alt['id']][$k['id']] > 0) {
                        $filled_cells++;
                    }
                }
            }
            $progress_percentage = $total_cells > 0 ? ($filled_cells / $total_cells) * 100 : 0;
            ?>
            
            <div class="card bg-light">
                <div class="card-body">
                    <h6>Progress Penilaian</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar" style="width: <?= $progress_percentage ?>%"></div>
                    </div>
                    <div class="row text-center">
                        <div class="col-md-3">
                            <strong><?= $filled_cells ?></strong><br>
                            <small class="text-muted">Terisi</small>
                        </div>
                        <div class="col-md-3">
                            <strong><?= $total_cells - $filled_cells ?></strong><br>
                            <small class="text-muted">Kosong</small>
                        </div>
                        <div class="col-md-3">
                            <strong><?= $total_cells ?></strong><br>
                            <small class="text-muted">Total</small>
                        </div>
                        <div class="col-md-3">
                            <strong><?= number_format($progress_percentage, 1) ?>%</strong><br>
                            <small class="text-muted">Selesai</small>
                        </div>
                    </div>
                    
                    <?php if ($progress_percentage == 100): ?>
                        <div class="alert alert-success mt-3 mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            Semua penilaian sudah lengkap! Anda dapat melanjutkan ke 
                            <a href="ranking.php" class="alert-link">perhitungan ranking</a>.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Panduan Penilaian -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-question-circle me-2"></i>Panduan Penilaian Kriteria</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <?php foreach ($kriteria as $k): ?>
            <div class="col-md-6 mb-3">
                <div class="card border-light">
                    <div class="card-body">
                        <h6 class="card-title">
                            <span class="badge bg-primary"><?= $k['kode'] ?></span> 
                            <?= htmlspecialchars($k['nama']) ?>
                            <span class="badge bg-<?= $k['jenis'] === 'benefit' ? 'success' : 'warning' ?> ms-2">
                                <?= ucfirst($k['jenis']) ?>
                            </span>
                        </h6>
                        <p class="card-text small"><?= htmlspecialchars($k['deskripsi']) ?></p>
                        <small class="text-muted">
                            <?php if ($k['jenis'] === 'benefit'): ?>
                                <i class="fas fa-arrow-up text-success"></i> Semakin tinggi nilai semakin baik
                            <?php else: ?>
                                <i class="fas fa-arrow-down text-warning"></i> Semakin rendah nilai semakin baik
                            <?php endif; ?>
                        </small>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<?php endif; ?>

<?php include 'includes/footer.php'; ?>
