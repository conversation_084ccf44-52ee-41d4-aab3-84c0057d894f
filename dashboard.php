<?php
require_once 'config/config.php';
requireLogin();

$page_title = 'Dashboard';

// Ambil statistik data
try {
    $db = getDB();
    
    // Hitung jumlah kriteria
    $stmt = $db->query("SELECT COUNT(*) as total FROM kriteria");
    $total_kriteria = $stmt->fetch()['total'];
    
    // Hitung jumlah alternatif
    $stmt = $db->query("SELECT COUNT(*) as total FROM alternatif");
    $total_alternatif = $stmt->fetch()['total'];
    
    // Hitung jumlah user (hanya untuk admin)
    $total_users = 0;
    if (isAdmin()) {
        $stmt = $db->query("SELECT COUNT(*) as total FROM users");
        $total_users = $stmt->fetch()['total'];
    }
    
    // Cek apakah sudah ada perbandingan kriteria
    $stmt = $db->query("SELECT COUNT(*) as total FROM perbandingan_kriteria");
    $total_perbandingan = $stmt->fetch()['total'];
    
    // Cek apakah sudah ada penilaian alternatif
    $stmt = $db->query("SELECT COUNT(*) as total FROM nilai_alternatif");
    $total_penilaian = $stmt->fetch()['total'];
    
    // Ambil hasil ranking terbaru
    $stmt = $db->query("
        SELECT r.*, a.nama as nama_alternatif 
        FROM hasil_ranking r 
        JOIN alternatif a ON r.alternatif_id = a.id 
        ORDER BY r.tanggal_hitung DESC, r.ranking ASC 
        LIMIT 5
    ");
    $ranking_terbaru = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error = "Terjadi kesalahan dalam mengambil data statistik.";
}

include 'includes/header.php';
?>

<div class="row">
    <!-- Statistics Cards -->
    <div class="col-6 col-md-3 mb-3 mb-md-4">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?= $total_kriteria ?></div>
                    <div class="d-none d-sm-block">Total Kriteria</div>
                    <div class="d-sm-none small">Kriteria</div>
                </div>
                <i class="fas fa-list fa-lg fa-md-2x opacity-75"></i>
            </div>
        </div>
    </div>

    <div class="col-6 col-md-3 mb-3 mb-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?= $total_alternatif ?></div>
                    <div class="d-none d-sm-block">Total Alternatif</div>
                    <div class="d-sm-none small">Alternatif</div>
                </div>
                <i class="fas fa-boxes fa-lg fa-md-2x opacity-75"></i>
            </div>
        </div>
    </div>

    <?php if (isAdmin()): ?>
    <div class="col-6 col-md-3 mb-3 mb-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?= $total_users ?></div>
                    <div class="d-none d-sm-block">Total Users</div>
                    <div class="d-sm-none small">Users</div>
                </div>
                <i class="fas fa-users fa-lg fa-md-2x opacity-75"></i>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="col-6 col-md-3 mb-3 mb-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #fa709a, #fee140);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?= count($ranking_terbaru) ?></div>
                    <div class="d-none d-sm-block">Hasil Ranking</div>
                    <div class="d-sm-none small">Ranking</div>
                </div>
                <i class="fas fa-trophy fa-lg fa-md-2x opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Progress Card -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Progress Sistem</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Data Kriteria</span>
                        <span><?= $total_kriteria > 0 ? '✓' : '✗' ?></span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" style="width: <?= $total_kriteria > 0 ? '100' : '0' ?>%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Data Alternatif</span>
                        <span><?= $total_alternatif > 0 ? '✓' : '✗' ?></span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" style="width: <?= $total_alternatif > 0 ? '100' : '0' ?>%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Perbandingan Kriteria (AHP)</span>
                        <span><?= $total_perbandingan > 0 ? '✓' : '✗' ?></span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" style="width: <?= $total_perbandingan > 0 ? '100' : '0' ?>%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Penilaian Alternatif</span>
                        <span><?= $total_penilaian > 0 ? '✓' : '✗' ?></span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" style="width: <?= $total_penilaian > 0 ? '100' : '0' ?>%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Latest Ranking -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Ranking Terbaru</h5>
            </div>
            <div class="card-body">
                <?php if (empty($ranking_terbaru)): ?>
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle fa-2x mb-2"></i>
                        <p>Belum ada hasil ranking.<br>Silakan lengkapi data dan lakukan perhitungan.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Alternatif</th>
                                    <th>Skor</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ranking_terbaru as $rank): ?>
                                <tr>
                                    <td>
                                        <?php if ($rank['ranking'] == 1): ?>
                                            <i class="fas fa-trophy text-warning"></i>
                                        <?php elseif ($rank['ranking'] == 2): ?>
                                            <i class="fas fa-medal text-secondary"></i>
                                        <?php elseif ($rank['ranking'] == 3): ?>
                                            <i class="fas fa-award text-warning"></i>
                                        <?php else: ?>
                                            <?= $rank['ranking'] ?>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= htmlspecialchars($rank['nama_alternatif']) ?></td>
                                    <td><?= formatNumber($rank['skor_topsis'], 4) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="ranking.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>Lihat Semua
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Aksi Cepat</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 col-md-3 mb-2">
                        <a href="kriteria.php" class="btn btn-outline-primary w-100 btn-mobile">
                            <i class="fas fa-list me-1 me-md-2"></i>
                            <span class="d-none d-sm-inline">Kelola </span>Kriteria
                        </a>
                    </div>
                    <div class="col-6 col-md-3 mb-2">
                        <a href="alternatif.php" class="btn btn-outline-success w-100 btn-mobile">
                            <i class="fas fa-boxes me-1 me-md-2"></i>
                            <span class="d-none d-sm-inline">Kelola </span>Alternatif
                        </a>
                    </div>
                    <div class="col-6 col-md-3 mb-2">
                        <a href="perbandingan.php" class="btn btn-outline-warning w-100 btn-mobile">
                            <i class="fas fa-balance-scale me-1 me-md-2"></i>
                            <span class="d-none d-sm-inline">Perbandingan </span>AHP
                        </a>
                    </div>
                    <div class="col-6 col-md-3 mb-2">
                        <a href="ranking.php" class="btn btn-outline-info w-100 btn-mobile">
                            <i class="fas fa-trophy me-1 me-md-2"></i>
                            <span class="d-none d-sm-inline">Lihat </span>Ranking
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
