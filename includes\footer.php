        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="<?= BASE_URL ?>assets/js/offline.js"></script>
    <script src="<?= BASE_URL ?>assets/js/notifications.js"></script>
    
    <script>
        // Mobile-friendly sidebar toggle
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.getElementById('mobileOverlay');
            const swipeIndicator = document.getElementById('swipeIndicator');

            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');

            // Hide swipe indicator after first use
            if (swipeIndicator) {
                swipeIndicator.style.display = 'none';
            }
        });

        // Close sidebar when clicking overlay
        document.getElementById('mobileOverlay')?.addEventListener('click', function() {
            document.querySelector('.sidebar').classList.remove('show');
            this.classList.remove('show');
        });

        // Touch/swipe support for mobile
        let startX = 0;
        let currentX = 0;
        let isDragging = false;

        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            isDragging = true;
        });

        document.addEventListener('touchmove', function(e) {
            if (!isDragging) return;
            currentX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', function(e) {
            if (!isDragging) return;
            isDragging = false;

            const diffX = currentX - startX;
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.getElementById('mobileOverlay');

            // Swipe right from left edge to open sidebar
            if (startX < 50 && diffX > 100 && window.innerWidth <= 768) {
                sidebar.classList.add('show');
                overlay.classList.add('show');

                // Hide swipe indicator
                const swipeIndicator = document.getElementById('swipeIndicator');
                if (swipeIndicator) {
                    swipeIndicator.style.display = 'none';
                }
            }

            // Swipe left to close sidebar
            if (diffX < -100 && sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            }
        });
        
        // Initialize DataTables with mobile-friendly settings
        $(document).ready(function() {
            $('.data-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/id.json'
                },
                responsive: true,
                pageLength: window.innerWidth <= 768 ? 5 : 10,
                lengthMenu: window.innerWidth <= 768 ?
                    [[5, 10, 25], [5, 10, 25]] :
                    [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
                scrollX: true,
                columnDefs: [
                    { responsivePriority: 1, targets: 0 },
                    { responsivePriority: 2, targets: -1 }
                ]
            });

            // Auto-hide swipe indicator after 5 seconds
            setTimeout(function() {
                const swipeIndicator = document.getElementById('swipeIndicator');
                if (swipeIndicator && window.innerWidth <= 768) {
                    swipeIndicator.style.opacity = '0';
                    setTimeout(function() {
                        swipeIndicator.style.display = 'none';
                    }, 500);
                }
            }, 5000);
        });
        
        // Confirm delete
        function confirmDelete(url, message = 'Apakah Anda yakin ingin menghapus data ini?') {
            Swal.fire({
                title: 'Konfirmasi Hapus',
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = url;
                }
            });
        }
        
        // Show success message
        function showSuccess(message) {
            Swal.fire({
                title: 'Berhasil!',
                text: message,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        }
        
        // Show error message
        function showError(message) {
            Swal.fire({
                title: 'Error!',
                text: message,
                icon: 'error'
            });
        }
    </script>
    
    <!-- PWA Installation and Service Worker -->
    <script>
        // Register Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        let installButton = null;

        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // Show install button if not already installed
            if (!window.matchMedia('(display-mode: standalone)').matches) {
                showInstallButton();
            }
        });

        function showInstallButton() {
            if (installButton) return;

            installButton = document.createElement('button');
            installButton.className = 'btn btn-success btn-sm position-fixed';
            installButton.style.cssText = 'bottom: 20px; right: 20px; z-index: 1050; border-radius: 25px; padding: 8px 16px;';
            installButton.innerHTML = '<i class="fas fa-download me-1"></i>Install App';
            installButton.onclick = installApp;

            document.body.appendChild(installButton);

            // Auto-hide after 10 seconds
            setTimeout(() => {
                if (installButton) {
                    installButton.style.opacity = '0.7';
                }
            }, 10000);
        }

        function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                        if (installButton) {
                            installButton.remove();
                            installButton = null;
                        }
                    }
                    deferredPrompt = null;
                });
            }
        }

        // Hide install button if app is already installed
        window.addEventListener('appinstalled', (evt) => {
            console.log('App was installed');
            if (installButton) {
                installButton.remove();
                installButton = null;
            }
        });

        // Detect if running as PWA
        if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true) {
            document.body.classList.add('pwa-mode');

            // Add PWA-specific styles
            const style = document.createElement('style');
            style.textContent = `
                .pwa-mode .top-navbar {
                    padding-top: env(safe-area-inset-top, 0.75rem);
                }
                .pwa-mode .content-wrapper {
                    padding-bottom: env(safe-area-inset-bottom, 1rem);
                }
            `;
            document.head.appendChild(style);
        }

        // Network status detection
        function updateNetworkStatus() {
            const isOnline = navigator.onLine;
            const statusElement = document.getElementById('networkStatus');

            if (!isOnline && !statusElement) {
                const offlineAlert = document.createElement('div');
                offlineAlert.id = 'networkStatus';
                offlineAlert.className = 'alert alert-warning position-fixed';
                offlineAlert.style.cssText = 'top: 10px; left: 50%; transform: translateX(-50%); z-index: 1060; margin: 0;';
                offlineAlert.innerHTML = '<i class="fas fa-wifi me-2"></i>Mode Offline - Beberapa fitur mungkin terbatas';
                document.body.appendChild(offlineAlert);
            } else if (isOnline && statusElement) {
                statusElement.remove();
            }
        }

        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);
        updateNetworkStatus();
    </script>

    <?php if (isset($extra_js)): ?>
        <?= $extra_js ?>
    <?php endif; ?>
</body>
</html>
