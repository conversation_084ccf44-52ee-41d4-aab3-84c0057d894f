<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Test Mobile Features - SPK Pupuk Organik</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/mobile.css" rel="stylesheet">
    <style>
        .test-section {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .test-result {
            padding: 0.5rem;
            border-radius: 4px;
            margin-top: 0.5rem;
        }
        .test-pass { background-color: #d4edda; color: #155724; }
        .test-fail { background-color: #f8d7da; color: #721c24; }
        .test-info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container-fluid p-3">
        <h2 class="text-center mb-4">
            <i class="fas fa-mobile-alt me-2"></i>
            Test Mobile Features
        </h2>

        <!-- Device Info Test -->
        <div class="test-section">
            <h5><i class="fas fa-info-circle me-2"></i>Device Information</h5>
            <div id="deviceInfo"></div>
        </div>

        <!-- Touch Test -->
        <div class="test-section">
            <h5><i class="fas fa-hand-point-up me-2"></i>Touch Test</h5>
            <div id="touchArea" class="bg-light p-4 text-center border rounded" style="min-height: 100px;">
                Touch/Click here to test touch events
            </div>
            <div id="touchResult" class="test-result test-info">
                Touch events will be displayed here
            </div>
        </div>

        <!-- Swipe Test -->
        <div class="test-section">
            <h5><i class="fas fa-arrows-alt-h me-2"></i>Swipe Test</h5>
            <div id="swipeArea" class="bg-light p-4 text-center border rounded" style="min-height: 100px;">
                Swipe left or right here
            </div>
            <div id="swipeResult" class="test-result test-info">
                Swipe gestures will be detected here
            </div>
        </div>

        <!-- Orientation Test -->
        <div class="test-section">
            <h5><i class="fas fa-sync-alt me-2"></i>Orientation Test</h5>
            <div id="orientationResult" class="test-result test-info">
                Current orientation will be shown here
            </div>
        </div>

        <!-- Network Test -->
        <div class="test-section">
            <h5><i class="fas fa-wifi me-2"></i>Network Test</h5>
            <div id="networkResult" class="test-result test-info">
                Network status will be shown here
            </div>
            <button class="btn btn-primary btn-sm mt-2" onclick="testNetworkSpeed()">
                Test Network Speed
            </button>
        </div>

        <!-- Storage Test -->
        <div class="test-section">
            <h5><i class="fas fa-database me-2"></i>Storage Test</h5>
            <div id="storageResult" class="test-result test-info">
                Storage capabilities will be tested here
            </div>
            <div class="mt-2">
                <button class="btn btn-success btn-sm me-2" onclick="testLocalStorage()">Test LocalStorage</button>
                <button class="btn btn-info btn-sm me-2" onclick="testIndexedDB()">Test IndexedDB</button>
                <button class="btn btn-warning btn-sm" onclick="testCacheAPI()">Test Cache API</button>
            </div>
        </div>

        <!-- PWA Test -->
        <div class="test-section">
            <h5><i class="fas fa-download me-2"></i>PWA Features Test</h5>
            <div id="pwaResult" class="test-result test-info">
                PWA capabilities will be tested here
            </div>
            <div class="mt-2">
                <button class="btn btn-primary btn-sm me-2" onclick="testServiceWorker()">Test Service Worker</button>
                <button class="btn btn-success btn-sm me-2" onclick="testManifest()">Test Manifest</button>
                <button class="btn btn-warning btn-sm" onclick="testNotifications()">Test Notifications</button>
            </div>
        </div>

        <!-- Performance Test -->
        <div class="test-section">
            <h5><i class="fas fa-tachometer-alt me-2"></i>Performance Test</h5>
            <div id="performanceResult" class="test-result test-info">
                Performance metrics will be shown here
            </div>
            <button class="btn btn-info btn-sm mt-2" onclick="testPerformance()">
                Run Performance Test
            </button>
        </div>

        <!-- Back to App -->
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-success">
                <i class="fas fa-arrow-left me-2"></i>Back to App
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            testDeviceInfo();
            testOrientation();
            testNetwork();
            setupTouchTest();
            setupSwipeTest();
            testPWAFeatures();
        });

        // Device Information Test
        function testDeviceInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'Platform': navigator.platform,
                'Screen Size': `${screen.width} × ${screen.height}`,
                'Viewport': `${window.innerWidth} × ${window.innerHeight}`,
                'Device Pixel Ratio': window.devicePixelRatio,
                'Touch Support': 'ontouchstart' in window ? 'Yes' : 'No',
                'Online': navigator.onLine ? 'Yes' : 'No'
            };

            let html = '<table class="table table-sm">';
            for (const [key, value] of Object.entries(info)) {
                html += `<tr><td><strong>${key}:</strong></td><td>${value}</td></tr>`;
            }
            html += '</table>';

            document.getElementById('deviceInfo').innerHTML = html;
        }

        // Touch Test
        function setupTouchTest() {
            const touchArea = document.getElementById('touchArea');
            const touchResult = document.getElementById('touchResult');

            touchArea.addEventListener('touchstart', function(e) {
                touchResult.innerHTML = `Touch Start: ${e.touches.length} finger(s)`;
                touchResult.className = 'test-result test-pass';
            });

            touchArea.addEventListener('touchmove', function(e) {
                touchResult.innerHTML = `Touch Move: ${e.touches.length} finger(s)`;
            });

            touchArea.addEventListener('touchend', function(e) {
                touchResult.innerHTML = `Touch End: Touch released`;
            });

            touchArea.addEventListener('click', function(e) {
                if (!('ontouchstart' in window)) {
                    touchResult.innerHTML = `Mouse Click: (${e.clientX}, ${e.clientY})`;
                    touchResult.className = 'test-result test-info';
                }
            });
        }

        // Swipe Test
        function setupSwipeTest() {
            const swipeArea = document.getElementById('swipeArea');
            const swipeResult = document.getElementById('swipeResult');
            let startX = 0;

            swipeArea.addEventListener('touchstart', function(e) {
                startX = e.touches[0].clientX;
            });

            swipeArea.addEventListener('touchend', function(e) {
                const endX = e.changedTouches[0].clientX;
                const diffX = endX - startX;

                if (Math.abs(diffX) > 50) {
                    const direction = diffX > 0 ? 'Right' : 'Left';
                    swipeResult.innerHTML = `Swipe ${direction} detected (${Math.abs(diffX)}px)`;
                    swipeResult.className = 'test-result test-pass';
                }
            });
        }

        // Orientation Test
        function testOrientation() {
            function updateOrientation() {
                const orientation = screen.orientation ? screen.orientation.type : 
                    (window.innerWidth > window.innerHeight ? 'landscape' : 'portrait');
                
                document.getElementById('orientationResult').innerHTML = 
                    `Current orientation: ${orientation}`;
            }

            updateOrientation();
            window.addEventListener('orientationchange', updateOrientation);
            window.addEventListener('resize', updateOrientation);
        }

        // Network Test
        function testNetwork() {
            function updateNetworkStatus() {
                const status = navigator.onLine ? 'Online' : 'Offline';
                const className = navigator.onLine ? 'test-pass' : 'test-fail';
                
                document.getElementById('networkResult').innerHTML = `Network Status: ${status}`;
                document.getElementById('networkResult').className = `test-result ${className}`;
            }

            updateNetworkStatus();
            window.addEventListener('online', updateNetworkStatus);
            window.addEventListener('offline', updateNetworkStatus);
        }

        function testNetworkSpeed() {
            const start = performance.now();
            fetch('https://httpbin.org/json')
                .then(response => response.json())
                .then(data => {
                    const end = performance.now();
                    const time = Math.round(end - start);
                    document.getElementById('networkResult').innerHTML += 
                        `<br>Network Speed Test: ${time}ms`;
                })
                .catch(error => {
                    document.getElementById('networkResult').innerHTML += 
                        `<br>Network Speed Test: Failed`;
                });
        }

        // Storage Tests
        function testLocalStorage() {
            try {
                localStorage.setItem('test', 'value');
                localStorage.removeItem('test');
                updateStorageResult('LocalStorage: ✓ Available');
            } catch (e) {
                updateStorageResult('LocalStorage: ✗ Not available');
            }
        }

        function testIndexedDB() {
            if ('indexedDB' in window) {
                updateStorageResult('IndexedDB: ✓ Available');
            } else {
                updateStorageResult('IndexedDB: ✗ Not available');
            }
        }

        function testCacheAPI() {
            if ('caches' in window) {
                updateStorageResult('Cache API: ✓ Available');
            } else {
                updateStorageResult('Cache API: ✗ Not available');
            }
        }

        function updateStorageResult(message) {
            const current = document.getElementById('storageResult').innerHTML;
            document.getElementById('storageResult').innerHTML = 
                current.includes('Storage capabilities') ? message : current + '<br>' + message;
        }

        // PWA Tests
        function testPWAFeatures() {
            let results = [];
            
            if ('serviceWorker' in navigator) {
                results.push('Service Worker: ✓ Supported');
            } else {
                results.push('Service Worker: ✗ Not supported');
            }

            if (window.matchMedia('(display-mode: standalone)').matches) {
                results.push('PWA Mode: ✓ Running as PWA');
            } else {
                results.push('PWA Mode: ✗ Running in browser');
            }

            if ('Notification' in window) {
                results.push(`Notifications: ✓ Supported (${Notification.permission})`);
            } else {
                results.push('Notifications: ✗ Not supported');
            }

            document.getElementById('pwaResult').innerHTML = results.join('<br>');
        }

        function testServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(registrations => {
                    const count = registrations.length;
                    updatePWAResult(`Service Workers: ${count} registered`);
                });
            }
        }

        function testManifest() {
            fetch('/manifest.json')
                .then(response => response.json())
                .then(manifest => {
                    updatePWAResult(`Manifest: ✓ Loaded (${manifest.name})`);
                })
                .catch(() => {
                    updatePWAResult('Manifest: ✗ Failed to load');
                });
        }

        function testNotifications() {
            if ('Notification' in window) {
                Notification.requestPermission().then(permission => {
                    updatePWAResult(`Notification Permission: ${permission}`);
                });
            }
        }

        function updatePWAResult(message) {
            const current = document.getElementById('pwaResult').innerHTML;
            document.getElementById('pwaResult').innerHTML = current + '<br>' + message;
        }

        // Performance Test
        function testPerformance() {
            const start = performance.now();
            
            // Simulate some work
            for (let i = 0; i < 100000; i++) {
                Math.random();
            }
            
            const end = performance.now();
            const time = Math.round(end - start);
            
            const memory = performance.memory ? 
                `Memory: ${Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)}MB` : 
                'Memory info not available';
            
            document.getElementById('performanceResult').innerHTML = 
                `Performance Test: ${time}ms<br>${memory}`;
        }
    </script>
</body>
</html>
