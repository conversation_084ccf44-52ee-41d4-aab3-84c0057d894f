<?php
require_once 'config/config.php';
requireLogin();

$page_title = 'Informasi Mobile';

include 'includes/header.php';
?>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-mobile-alt me-2"></i>Informasi Mobile & PWA</h5>
    </div>
    <div class="card-body">
        <!-- PWA Status -->
        <div class="row mb-4">
            <div class="col-12">
                <h6><i class="fas fa-download me-2"></i>Status Aplikasi</h6>
                <div id="pwaStatus" class="alert alert-info">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        Memeriksa status aplikasi...
                    </div>
                </div>
            </div>
        </div>

        <!-- Network Status -->
        <div class="row mb-4">
            <div class="col-12">
                <h6><i class="fas fa-wifi me-2"></i>Status Koneksi</h6>
                <div id="networkInfo" class="alert">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        Memeriksa koneksi...
                    </div>
                </div>
            </div>
        </div>

        <!-- Storage Info -->
        <div class="row mb-4">
            <div class="col-12">
                <h6><i class="fas fa-database me-2"></i>Penyimpanan Offline</h6>
                <div id="storageInfo" class="alert alert-secondary">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        Menghitung penggunaan penyimpanan...
                    </div>
                </div>
                <button class="btn btn-outline-danger btn-sm mt-2" onclick="clearOfflineData()">
                    <i class="fas fa-trash me-1"></i>Hapus Data Offline
                </button>
            </div>
        </div>

        <!-- Device Info -->
        <div class="row mb-4">
            <div class="col-12">
                <h6><i class="fas fa-info-circle me-2"></i>Informasi Perangkat</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <tbody>
                            <tr>
                                <td><strong>User Agent:</strong></td>
                                <td><small id="userAgent">-</small></td>
                            </tr>
                            <tr>
                                <td><strong>Ukuran Layar:</strong></td>
                                <td id="screenSize">-</td>
                            </tr>
                            <tr>
                                <td><strong>Viewport:</strong></td>
                                <td id="viewport">-</td>
                            </tr>
                            <tr>
                                <td><strong>Orientasi:</strong></td>
                                <td id="orientation">-</td>
                            </tr>
                            <tr>
                                <td><strong>Touch Support:</strong></td>
                                <td id="touchSupport">-</td>
                            </tr>
                            <tr>
                                <td><strong>Service Worker:</strong></td>
                                <td id="swSupport">-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Mobile Tips -->
        <div class="row">
            <div class="col-12">
                <h6><i class="fas fa-lightbulb me-2"></i>Tips Penggunaan Mobile</h6>
                <div class="accordion" id="mobileTips">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#tip1">
                                <i class="fas fa-download me-2"></i>Install sebagai Aplikasi
                            </button>
                        </h2>
                        <div id="tip1" class="accordion-collapse collapse" data-bs-parent="#mobileTips">
                            <div class="accordion-body">
                                <p>Untuk pengalaman terbaik, install aplikasi ini ke home screen perangkat Anda:</p>
                                <ul>
                                    <li><strong>Android Chrome:</strong> Tap menu (⋮) → "Add to Home screen"</li>
                                    <li><strong>iOS Safari:</strong> Tap Share (□↗) → "Add to Home Screen"</li>
                                    <li><strong>Desktop:</strong> Klik icon install di address bar</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#tip2">
                                <i class="fas fa-wifi me-2"></i>Mode Offline
                            </button>
                        </h2>
                        <div id="tip2" class="accordion-collapse collapse" data-bs-parent="#mobileTips">
                            <div class="accordion-body">
                                <p>Aplikasi ini mendukung mode offline:</p>
                                <ul>
                                    <li>Data akan disimpan secara lokal saat offline</li>
                                    <li>Sinkronisasi otomatis saat koneksi kembali</li>
                                    <li>Halaman yang sudah dibuka dapat diakses offline</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#tip3">
                                <i class="fas fa-hand-point-left me-2"></i>Navigasi Gesture
                            </button>
                        </h2>
                        <div id="tip3" class="accordion-collapse collapse" data-bs-parent="#mobileTips">
                            <div class="accordion-body">
                                <p>Gunakan gesture untuk navigasi yang lebih mudah:</p>
                                <ul>
                                    <li><strong>Swipe dari kiri:</strong> Buka menu sidebar</li>
                                    <li><strong>Swipe kiri pada sidebar:</strong> Tutup menu</li>
                                    <li><strong>Tap di luar sidebar:</strong> Tutup menu</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    updatePWAStatus();
    updateNetworkInfo();
    updateDeviceInfo();
    updateStorageInfo();
    
    // Update network info when status changes
    window.addEventListener('online', updateNetworkInfo);
    window.addEventListener('offline', updateNetworkInfo);
});

function updatePWAStatus() {
    const statusDiv = document.getElementById('pwaStatus');
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true;
    
    if (isStandalone) {
        statusDiv.className = 'alert alert-success';
        statusDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>Aplikasi terinstall sebagai PWA';
    } else {
        statusDiv.className = 'alert alert-warning';
        statusDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Belum terinstall sebagai PWA. <button class="btn btn-sm btn-primary ms-2" onclick="showInstallPrompt()">Install Sekarang</button>';
    }
}

function updateNetworkInfo() {
    const networkDiv = document.getElementById('networkInfo');
    const isOnline = navigator.onLine;
    
    if (isOnline) {
        networkDiv.className = 'alert alert-success';
        networkDiv.innerHTML = '<i class="fas fa-wifi me-2"></i>Online - Koneksi tersedia';
    } else {
        networkDiv.className = 'alert alert-warning';
        networkDiv.innerHTML = '<i class="fas fa-wifi me-2"></i>Offline - Mode offline aktif';
    }
}

function updateDeviceInfo() {
    document.getElementById('userAgent').textContent = navigator.userAgent;
    document.getElementById('screenSize').textContent = `${screen.width} × ${screen.height}`;
    document.getElementById('viewport').textContent = `${window.innerWidth} × ${window.innerHeight}`;
    document.getElementById('orientation').textContent = screen.orientation ? screen.orientation.type : 'Unknown';
    document.getElementById('touchSupport').textContent = 'ontouchstart' in window ? 'Ya' : 'Tidak';
    document.getElementById('swSupport').textContent = 'serviceWorker' in navigator ? 'Ya' : 'Tidak';
}

async function updateStorageInfo() {
    const storageDiv = document.getElementById('storageInfo');
    
    try {
        if (window.offlineManager) {
            const cacheSize = await window.offlineManager.getCacheSize();
            const sizeKB = (cacheSize / 1024).toFixed(2);
            
            storageDiv.className = 'alert alert-info';
            storageDiv.innerHTML = `<i class="fas fa-database me-2"></i>Data offline: ${sizeKB} KB`;
        } else {
            storageDiv.className = 'alert alert-secondary';
            storageDiv.innerHTML = '<i class="fas fa-database me-2"></i>Offline manager tidak tersedia';
        }
    } catch (error) {
        storageDiv.className = 'alert alert-danger';
        storageDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Error mengakses penyimpanan';
    }
}

function showInstallPrompt() {
    if (window.deferredPrompt) {
        window.deferredPrompt.prompt();
    } else {
        alert('Install prompt tidak tersedia. Gunakan menu browser untuk menambahkan ke home screen.');
    }
}

async function clearOfflineData() {
    if (confirm('Hapus semua data offline? Data yang belum tersinkronisasi akan hilang.')) {
        try {
            if (window.offlineManager) {
                await window.offlineManager.clearOfflineCache();
                updateStorageInfo();
                alert('Data offline berhasil dihapus');
            }
        } catch (error) {
            alert('Gagal menghapus data offline');
        }
    }
}
</script>

<?php include 'includes/footer.php'; ?>
