<?php
require_once 'config/config.php';
require_once 'classes/TOPSIS.php';
requireLogin();

$page_title = 'Hasil Ranking TOPSIS';
$success = '';
$error = '';

$db = getDB();
$topsis = new TOPSIS($db);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'calculate') {
        if (!$topsis->isDataComplete()) {
            $error = 'Data belum lengkap! Pastikan semua kriteria memiliki bobot dan semua alternatif memiliki nilai.';
        } else {
            $result = $topsis->calculateTOPSIS();
            if (!empty($result['results'])) {
                if ($topsis->saveRanking($result['results'])) {
                    $success = 'Perhitungan TOPSIS berhasil! Ranking telah diperbarui.';
                } else {
                    $error = 'Perhitungan berhasil tetapi gagal menyimpan ke database.';
                }
            } else {
                $error = 'Gagal melakukan perhitungan TOPSIS.';
            }
        }
    }
}

// Ambil data untuk perhitungan
$topsisResult = $topsis->calculateTOPSIS();
$latestRanking = $topsis->getLatestRanking();
$isDataComplete = $topsis->isDataComplete();

// Ambil statistik
try {
    $stmt = $db->query("SELECT COUNT(*) FROM kriteria WHERE bobot > 0");
    $kriteriaWithWeight = $stmt->fetchColumn();
    
    $stmt = $db->query("SELECT COUNT(*) FROM alternatif");
    $totalAlternatif = $stmt->fetchColumn();
    
    $stmt = $db->query("
        SELECT COUNT(*) 
        FROM nilai_alternatif na 
        JOIN kriteria k ON na.kriteria_id = k.id 
        WHERE k.bobot > 0
    ");
    $totalNilai = $stmt->fetchColumn();
    
    $expectedNilai = $kriteriaWithWeight * $totalAlternatif;
    
} catch (Exception $e) {
    $kriteriaWithWeight = 0;
    $totalAlternatif = 0;
    $totalNilai = 0;
    $expectedNilai = 0;
}

include 'includes/header.php';
?>

<?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?= $success ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i><?= $error ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Status Data -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Status Data</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-6 col-md-3 mb-3 mb-md-0">
                <div class="text-center">
                    <h4 class="<?= $kriteriaWithWeight > 0 ? 'text-success' : 'text-danger' ?>">
                        <?= $kriteriaWithWeight ?>
                    </h4>
                    <small>Kriteria dengan Bobot</small>
                </div>
            </div>
            <div class="col-6 col-md-3 mb-3 mb-md-0">
                <div class="text-center">
                    <h4 class="<?= $totalAlternatif > 0 ? 'text-success' : 'text-danger' ?>">
                        <?= $totalAlternatif ?>
                    </h4>
                    <small>Total Alternatif</small>
                </div>
            </div>
            <div class="col-6 col-md-3 mb-3 mb-md-0">
                <div class="text-center">
                    <h4 class="<?= $totalNilai >= $expectedNilai && $expectedNilai > 0 ? 'text-success' : 'text-warning' ?>">
                        <?= $totalNilai ?>/<?= $expectedNilai ?>
                    </h4>
                    <small>Penilaian Lengkap</small>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="text-center">
                    <?php if ($isDataComplete): ?>
                        <button class="btn btn-success btn-mobile w-100" onclick="calculateRanking()">
                            <i class="fas fa-calculator me-1 me-md-2"></i>
                            <span class="d-none d-sm-inline">Hitung </span>Ranking
                        </button>
                    <?php else: ?>
                        <button class="btn btn-secondary btn-mobile w-100" disabled>
                            <i class="fas fa-exclamation-triangle me-1 me-md-2"></i>
                            <span class="d-none d-sm-inline">Data </span>Belum Lengkap
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <?php if (!$isDataComplete): ?>
        <div class="alert alert-warning mt-3 mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Data belum lengkap untuk perhitungan TOPSIS:</strong>
            <ul class="mb-0 mt-2">
                <?php if ($kriteriaWithWeight == 0): ?>
                    <li>Belum ada kriteria dengan bobot. <a href="perbandingan.php">Lakukan perbandingan AHP</a> terlebih dahulu.</li>
                <?php endif; ?>
                <?php if ($totalAlternatif == 0): ?>
                    <li>Belum ada alternatif. <a href="alternatif.php">Tambah alternatif</a> terlebih dahulu.</li>
                <?php endif; ?>
                <?php if ($totalNilai < $expectedNilai && $expectedNilai > 0): ?>
                    <li>Penilaian alternatif belum lengkap. <a href="penilaian.php">Lengkapi penilaian</a> terlebih dahulu.</li>
                <?php endif; ?>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Hasil Ranking -->
<?php if (!empty($latestRanking)): ?>
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Hasil Ranking TOPSIS</h5>
        <div class="d-flex align-items-center">
            <small class="text-muted me-3">
                Terakhir dihitung: <?= date('d/m/Y', strtotime($latestRanking[0]['tanggal_hitung'])) ?>
            </small>
            <a href="laporan.php" class="btn btn-success btn-sm" target="_blank">
                <i class="fas fa-file-pdf me-1"></i>Cetak Laporan
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Ranking</th>
                        <th>Kode</th>
                        <th>Nama Alternatif</th>
                        <th>Skor TOPSIS</th>
                        <th>Persentase</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($latestRanking as $rank): ?>
                    <tr class="<?= $rank['ranking'] == 1 ? 'table-success' : '' ?>">
                        <td>
                            <?php if ($rank['ranking'] == 1): ?>
                                <i class="fas fa-trophy text-warning fa-lg"></i>
                            <?php elseif ($rank['ranking'] == 2): ?>
                                <i class="fas fa-medal text-secondary fa-lg"></i>
                            <?php elseif ($rank['ranking'] == 3): ?>
                                <i class="fas fa-award text-warning fa-lg"></i>
                            <?php else: ?>
                                <span class="badge bg-primary"><?= $rank['ranking'] ?></span>
                            <?php endif; ?>
                        </td>
                        <td><span class="badge bg-success"><?= htmlspecialchars($rank['kode']) ?></span></td>
                        <td>
                            <strong><?= htmlspecialchars($rank['nama']) ?></strong>
                            <?php if ($rank['deskripsi']): ?>
                                <br><small class="text-muted"><?= htmlspecialchars($rank['deskripsi']) ?></small>
                            <?php endif; ?>
                        </td>
                        <td><?= formatNumber($rank['skor_topsis'], 6) ?></td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar" style="width: <?= $rank['skor_topsis'] * 100 ?>%">
                                    <?= formatNumber($rank['skor_topsis'] * 100, 2) ?>%
                                </div>
                            </div>
                        </td>
                        <td>
                            <?php if ($rank['ranking'] == 1): ?>
                                <span class="badge bg-success">Terbaik</span>
                            <?php elseif ($rank['ranking'] <= 3): ?>
                                <span class="badge bg-info">Sangat Baik</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Baik</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Detail Perhitungan TOPSIS -->
<?php if (!empty($topsisResult['results']) && $isDataComplete): ?>
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Detail Perhitungan TOPSIS</h5>
    </div>
    <div class="card-body">
        <!-- Matriks Keputusan -->
        <h6><i class="fas fa-table me-2"></i>1. Matriks Keputusan</h6>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>Alternatif</th>
                        <?php foreach ($topsisResult['kriteria'] as $k): ?>
                            <th class="text-center"><?= $k['kode'] ?></th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($topsisResult['alternatif'] as $i => $alt): ?>
                    <tr>
                        <td><strong><?= $alt['kode'] ?></strong></td>
                        <?php foreach ($topsisResult['kriteria'] as $j => $k): ?>
                            <td class="text-center"><?= formatNumber($topsisResult['matrix'][$i][$j], 2) ?></td>
                        <?php endforeach; ?>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Matriks Ternormalisasi -->
        <h6><i class="fas fa-table me-2"></i>2. Matriks Ternormalisasi</h6>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>Alternatif</th>
                        <?php foreach ($topsisResult['kriteria'] as $k): ?>
                            <th class="text-center"><?= $k['kode'] ?></th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($topsisResult['alternatif'] as $i => $alt): ?>
                    <tr>
                        <td><strong><?= $alt['kode'] ?></strong></td>
                        <?php foreach ($topsisResult['kriteria'] as $j => $k): ?>
                            <td class="text-center"><?= formatNumber($topsisResult['normalizedMatrix'][$i][$j], 4) ?></td>
                        <?php endforeach; ?>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Matriks Terbobot -->
        <h6><i class="fas fa-table me-2"></i>3. Matriks Keputusan Terbobot</h6>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>Alternatif</th>
                        <?php foreach ($topsisResult['kriteria'] as $k): ?>
                            <th class="text-center">
                                <?= $k['kode'] ?><br>
                                <small>(w=<?= formatNumber($k['bobot'], 3) ?>)</small>
                            </th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($topsisResult['alternatif'] as $i => $alt): ?>
                    <tr>
                        <td><strong><?= $alt['kode'] ?></strong></td>
                        <?php foreach ($topsisResult['kriteria'] as $j => $k): ?>
                            <td class="text-center"><?= formatNumber($topsisResult['weightedMatrix'][$i][$j], 4) ?></td>
                        <?php endforeach; ?>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Solusi Ideal -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h6><i class="fas fa-plus-circle text-success me-2"></i>4. Solusi Ideal Positif (A+)</h6>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-success">
                            <tr>
                                <?php foreach ($topsisResult['kriteria'] as $k): ?>
                                    <th class="text-center"><?= $k['kode'] ?></th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <?php foreach ($topsisResult['idealSolutions']['positive'] as $value): ?>
                                    <td class="text-center"><?= formatNumber($value, 4) ?></td>
                                <?php endforeach; ?>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-minus-circle text-danger me-2"></i>5. Solusi Ideal Negatif (A-)</h6>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-danger">
                            <tr>
                                <?php foreach ($topsisResult['kriteria'] as $k): ?>
                                    <th class="text-center"><?= $k['kode'] ?></th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <?php foreach ($topsisResult['idealSolutions']['negative'] as $value): ?>
                                    <td class="text-center"><?= formatNumber($value, 4) ?></td>
                                <?php endforeach; ?>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Jarak dan Skor -->
        <h6><i class="fas fa-ruler me-2"></i>6. Jarak dan Skor TOPSIS</h6>
        <div class="table-responsive">
            <table class="table table-bordered table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>Alternatif</th>
                        <th class="text-center">D+ (Jarak ke A+)</th>
                        <th class="text-center">D- (Jarak ke A-)</th>
                        <th class="text-center">Skor TOPSIS</th>
                        <th class="text-center">Ranking</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($topsisResult['results'] as $result): ?>
                    <tr class="<?= $result['ranking'] == 1 ? 'table-success' : '' ?>">
                        <td><strong><?= $result['alternatif']['kode'] ?></strong></td>
                        <td class="text-center"><?= formatNumber($result['jarak_positif'], 4) ?></td>
                        <td class="text-center"><?= formatNumber($result['jarak_negatif'], 4) ?></td>
                        <td class="text-center"><strong><?= formatNumber($result['skor'], 6) ?></strong></td>
                        <td class="text-center">
                            <?php if ($result['ranking'] == 1): ?>
                                <i class="fas fa-trophy text-warning"></i>
                            <?php else: ?>
                                <?= $result['ranking'] ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
$extra_js = "
<script>
function calculateRanking() {
    if (confirm('Hitung ulang ranking TOPSIS? Hasil sebelumnya akan ditimpa.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"calculate\">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
";

include 'includes/footer.php';
?>
