/* Mobile-specific styles for SPK Pupuk Organik */

/* Touch-friendly interactions */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
}

/* Mobile table improvements */
@media (max-width: 768px) {
    .table-responsive {
        border: none;
        font-size: 0.875rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        vertical-align: middle;
    }
    
    .table th {
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    /* Mobile-friendly badges */
    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* Mobile form improvements */
    .form-control,
    .form-select {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 0.75rem;
        border-radius: 8px;
    }
    
    .input-group-sm .form-control,
    .input-group-sm .form-select {
        font-size: 14px;
        padding: 0.5rem;
    }
    
    /* Mobile modal improvements */
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .modal-header {
        padding: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-footer {
        padding: 1rem;
        flex-wrap: wrap;
    }
    
    .modal-footer .btn {
        margin: 0.25rem;
        flex: 1;
    }
    
    /* Mobile card improvements */
    .card {
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .card-header {
        padding: 1rem;
        border-radius: 12px 12px 0 0;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* Mobile stats cards */
    .stats-card {
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 0.75rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .stats-card .stats-number {
        font-size: 1.5rem;
        font-weight: bold;
        line-height: 1.2;
    }
    
    /* Mobile navigation improvements */
    .sidebar-menu .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        border-radius: 8px;
        margin: 0.25rem 0.5rem;
    }
    
    .sidebar-menu .nav-link i {
        width: 20px;
        text-align: center;
    }
    
    /* Mobile progress bars */
    .progress {
        height: 20px;
        border-radius: 10px;
        background-color: rgba(0,0,0,0.1);
    }
    
    .progress-bar {
        border-radius: 10px;
        font-size: 0.75rem;
        line-height: 20px;
    }
    
    /* Mobile alerts */
    .alert {
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }
    
    .alert .btn-close {
        padding: 0.5rem;
    }
    
    /* Mobile dropdown improvements */
    .dropdown-menu {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border: none;
    }
    
    .dropdown-item {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    /* Mobile button groups */
    .btn-group-vertical .btn {
        margin-bottom: 0.5rem;
        border-radius: 8px !important;
    }
    
    /* Mobile specific utilities */
    .text-mobile-center {
        text-align: center;
    }
    
    .mb-mobile-3 {
        margin-bottom: 1rem;
    }
    
    .p-mobile-2 {
        padding: 0.5rem;
    }
}

/* Extra small devices */
@media (max-width: 576px) {
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    
    .content-wrapper {
        padding: 0 0.75rem 1rem;
    }
    
    .top-navbar {
        padding: 0.5rem 0.75rem;
    }
    
    .card {
        margin-bottom: 0.75rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    
    .btn-sm {
        font-size: 0.8rem;
        padding: 0.375rem 0.5rem;
    }
    
    h1, .h1 { font-size: 1.5rem; }
    h2, .h2 { font-size: 1.35rem; }
    h3, .h3 { font-size: 1.2rem; }
    h4, .h4 { font-size: 1.1rem; }
    h5, .h5 { font-size: 1rem; }
    h6, .h6 { font-size: 0.9rem; }
    
    .stats-card .stats-number {
        font-size: 1.25rem;
    }
    
    .table th,
    .table td {
        padding: 0.375rem 0.25rem;
        font-size: 0.8rem;
    }
}

/* Landscape orientation on mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .sidebar {
        width: 200px;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .top-navbar {
        padding: 0.5rem 1rem;
    }
    
    .content-wrapper {
        padding: 0 1rem 1rem;
    }
    
    .stats-card {
        padding: 0.75rem;
    }
    
    .stats-card .stats-number {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .btn {
        border-width: 0.5px;
    }
    
    .card {
        border-width: 0.5px;
    }
    
    .table {
        border-width: 0.5px;
    }
}

/* Dark mode support for mobile */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .table {
        color: #e2e8f0;
    }
    
    .form-control,
    .form-select {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .modal-content {
        background-color: #2d3748;
        color: #e2e8f0;
    }
}

/* Accessibility improvements for mobile */
@media (max-width: 768px) {
    /* Larger touch targets */
    .btn,
    .form-control,
    .form-select,
    .nav-link {
        min-height: 44px;
    }
    
    /* Better focus indicators */
    .btn:focus,
    .form-control:focus,
    .form-select:focus,
    .nav-link:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
    }
    
    /* Improved text contrast */
    .text-muted {
        color: #6c757d !important;
    }
    
    /* Better spacing for readability */
    p, .card-text {
        line-height: 1.6;
    }
    
    /* Larger clickable areas */
    .dropdown-toggle::after {
        margin-left: 0.5rem;
    }
}

/* Animation improvements for mobile */
@media (max-width: 768px) {
    .sidebar {
        transition: transform 0.3s ease-in-out;
    }
    
    .mobile-overlay {
        transition: opacity 0.3s ease-in-out;
    }
    
    .btn {
        transition: all 0.2s ease-in-out;
    }
    
    .card {
        transition: box-shadow 0.2s ease-in-out;
    }
    
    .card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
}

/* Print styles for mobile */
@media print {
    .sidebar,
    .top-navbar,
    .mobile-overlay,
    .swipe-indicator,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .content-wrapper {
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 10px !important;
    }
}
