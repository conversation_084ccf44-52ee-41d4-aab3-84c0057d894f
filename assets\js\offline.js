// Offline functionality for SPK Pupuk Organik

class OfflineManager {
    constructor() {
        this.dbName = 'spk_pupuk_offline';
        this.dbVersion = 1;
        this.db = null;
        this.init();
    }

    async init() {
        try {
            this.db = await this.openDB();
            this.setupEventListeners();
        } catch (error) {
            console.error('Failed to initialize offline manager:', error);
        }
    }

    openDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores for offline data
                if (!db.objectStoreNames.contains('kriteria')) {
                    db.createObjectStore('kriteria', { keyPath: 'id' });
                }
                
                if (!db.objectStoreNames.contains('alternatif')) {
                    db.createObjectStore('alternatif', { keyPath: 'id' });
                }
                
                if (!db.objectStoreNames.contains('penilaian')) {
                    db.createObjectStore('penilaian', { keyPath: 'id', autoIncrement: true });
                }
                
                if (!db.objectStoreNames.contains('perbandingan')) {
                    db.createObjectStore('perbandingan', { keyPath: 'id', autoIncrement: true });
                }
                
                if (!db.objectStoreNames.contains('sync_queue')) {
                    db.createObjectStore('sync_queue', { keyPath: 'id', autoIncrement: true });
                }
            };
        });
    }

    setupEventListeners() {
        // Listen for online/offline events
        window.addEventListener('online', () => this.syncOfflineData());
        window.addEventListener('offline', () => this.showOfflineMessage());
        
        // Intercept form submissions when offline
        document.addEventListener('submit', (event) => {
            if (!navigator.onLine) {
                event.preventDefault();
                this.handleOfflineSubmission(event.target);
            }
        });
    }

    async saveOfflineData(storeName, data) {
        if (!this.db) return false;
        
        try {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            await store.put(data);
            return true;
        } catch (error) {
            console.error('Failed to save offline data:', error);
            return false;
        }
    }

    async getOfflineData(storeName, key = null) {
        if (!this.db) return null;
        
        try {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            
            if (key) {
                return await store.get(key);
            } else {
                return await store.getAll();
            }
        } catch (error) {
            console.error('Failed to get offline data:', error);
            return null;
        }
    }

    async addToSyncQueue(action, data) {
        const queueItem = {
            action: action,
            data: data,
            timestamp: Date.now(),
            synced: false
        };
        
        return await this.saveOfflineData('sync_queue', queueItem);
    }

    async syncOfflineData() {
        if (!navigator.onLine || !this.db) return;
        
        try {
            const queueItems = await this.getOfflineData('sync_queue');
            const unsynced = queueItems.filter(item => !item.synced);
            
            for (const item of unsynced) {
                try {
                    await this.syncItem(item);
                    
                    // Mark as synced
                    item.synced = true;
                    await this.saveOfflineData('sync_queue', item);
                    
                } catch (error) {
                    console.error('Failed to sync item:', error);
                }
            }
            
            if (unsynced.length > 0) {
                this.showSyncSuccessMessage(unsynced.length);
            }
            
        } catch (error) {
            console.error('Failed to sync offline data:', error);
        }
    }

    async syncItem(item) {
        const formData = new FormData();
        
        // Add action and data to form
        for (const key in item.data) {
            formData.append(key, item.data[key]);
        }
        
        const response = await fetch(item.action, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response;
    }

    handleOfflineSubmission(form) {
        const formData = new FormData(form);
        const data = {};
        
        // Convert FormData to object
        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        // Add to sync queue
        this.addToSyncQueue(form.action, data);
        
        // Show offline message
        this.showOfflineSubmissionMessage();
    }

    showOfflineMessage() {
        this.showMessage('Mode Offline', 'Anda sedang offline. Beberapa fitur mungkin terbatas.', 'warning');
    }

    showOfflineSubmissionMessage() {
        this.showMessage(
            'Data Disimpan Offline', 
            'Data Anda telah disimpan secara lokal dan akan disinkronkan ketika koneksi tersedia.', 
            'info'
        );
    }

    showSyncSuccessMessage(count) {
        this.showMessage(
            'Sinkronisasi Berhasil', 
            `${count} data berhasil disinkronkan ke server.`, 
            'success'
        );
    }

    showMessage(title, message, type = 'info') {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: title,
                text: message,
                icon: type,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        } else {
            // Fallback to browser alert
            alert(`${title}: ${message}`);
        }
    }

    // Cache management
    async clearOfflineCache() {
        if (!this.db) return;
        
        const stores = ['kriteria', 'alternatif', 'penilaian', 'perbandingan'];
        
        for (const storeName of stores) {
            try {
                const transaction = this.db.transaction([storeName], 'readwrite');
                const store = transaction.objectStore(storeName);
                await store.clear();
            } catch (error) {
                console.error(`Failed to clear ${storeName}:`, error);
            }
        }
    }

    // Get cache size
    async getCacheSize() {
        if (!this.db) return 0;
        
        let totalSize = 0;
        const stores = ['kriteria', 'alternatif', 'penilaian', 'perbandingan', 'sync_queue'];
        
        for (const storeName of stores) {
            try {
                const data = await this.getOfflineData(storeName);
                totalSize += JSON.stringify(data).length;
            } catch (error) {
                console.error(`Failed to get size for ${storeName}:`, error);
            }
        }
        
        return totalSize;
    }
}

// Initialize offline manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if ('indexedDB' in window) {
        window.offlineManager = new OfflineManager();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OfflineManager;
}
