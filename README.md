# Sistem Pendukung Keputusan Pemilihan Pupuk Organik

Sistem Pendukung Keputusan (SPK) untuk pemilihan komposisi pupuk organik pada rumput gajah mini menggunakan metode AHP (Analytical Hierarchy Process) dan TOPSIS (Technique for Order Preference by Similarity to Ideal Solution).

## Fitur Utama

### 1. Manajemen Data

- **Manajemen User**: CRUD user dengan role admin dan user
- **Manajemen Kriteria**: CRUD kriteria dengan jenis benefit/cost
- **Manajemen Alternatif**: CRUD alternatif pupuk organik
- **Penilaian Alternatif**: Input nilai alternatif untuk setiap kriteria

### 2. Metode Perhitungan

- **AHP (Analytical Hierarchy Process)**:
  - Perbandingan berpasangan kriteria
  - Perhitungan bobot kriteria
  - Consistency Ratio (CR) untuk validasi konsistensi
- **TOPSIS**:
  - Normalisasi matriks keputusan
  - Matriks keputusan terbobot
  - Solusi ideal positif dan negatif
  - Perhitungan jarak dan skor TOPSIS
  - Ranking alternatif

### 3. Interface & Laporan

- Dashboard dengan statistik dan progress
- Interface responsive dengan Bootstrap 5
- Laporan hasil yang dapat dicetak
- Visualisasi progress dan hasil ranking

## Kriteria Default

1. **Kebutuhan Nutrisi (C1)** - Benefit
2. **Kondisi Tanah (C2)** - Benefit
3. **Daya Serap (C3)** - Benefit
4. **Biaya (C4)** - Cost
5. **Ketersediaan (C5)** - Benefit
6. **Dampak Lingkungan (C6)** - Benefit

## Alternatif Default

1. **Kompos Bokashi (A1)**
2. **Pupuk Kandang (A2)**
3. **Pupuk Organik Cair (A3)**
4. **Pupuk Organik Padat Komersial (A4)**

## Instalasi

### Persyaratan Sistem

- PHP 7.4 atau lebih tinggi
- MySQL 5.7 atau lebih tinggi
- Web server (Apache/Nginx)
- Browser modern dengan JavaScript enabled

### Langkah Instalasi

1. **Clone atau download project**

   ```bash
   git clone [repository-url]
   cd "Aplikasi Pemilihan pupuk Organik"
   ```

2. **Setup Database**

   - Buat database MySQL baru
   - Import file `database.sql`
   - Sesuaikan konfigurasi database di `config/database.php`

3. **Konfigurasi**

   - Sesuaikan `BASE_URL` di `config/config.php`
   - Pastikan folder memiliki permission yang tepat

4. **Akses Aplikasi**
   - Buka browser dan akses URL aplikasi
   - Login dengan akun default:
     - Username: `admin`
     - Password: `password`

## Cara Penggunaan

### 1. Setup Awal

1. Login sebagai admin
2. Kelola data kriteria (sudah ada default)
3. Kelola data alternatif (sudah ada default)

### 2. Perhitungan AHP

1. Masuk ke menu "Perbandingan AHP"
2. Lakukan perbandingan berpasangan untuk semua kriteria
3. Klik "Hitung Bobot" untuk menghitung bobot kriteria
4. Pastikan Consistency Ratio (CR) ≤ 0.1

### 3. Penilaian Alternatif

1. Masuk ke menu "Penilaian Alternatif"
2. Berikan nilai 1-10 untuk setiap alternatif pada setiap kriteria
3. Pastikan semua penilaian sudah lengkap (100%)

### 4. Hasil Ranking

1. Masuk ke menu "Hasil Ranking"
2. Klik "Hitung Ranking" untuk menjalankan TOPSIS
3. Lihat hasil ranking dan detail perhitungan
4. Cetak laporan jika diperlukan

### 5. Penggunaan Mobile

1. **Install sebagai PWA**: Tap "Add to Home Screen" di browser mobile
2. **Navigasi Gesture**: Swipe dari kiri untuk membuka menu
3. **Mode Offline**: Aplikasi tetap berfungsi tanpa internet
4. **Notifikasi**: Aktifkan notifikasi untuk update penting
5. **Info Mobile**: Akses menu "Info Mobile" untuk status aplikasi

## Struktur File

```
├── config/
│   ├── config.php          # Konfigurasi aplikasi
│   └── database.php        # Konfigurasi database
├── classes/
│   ├── AHP.php            # Class untuk metode AHP
│   └── TOPSIS.php         # Class untuk metode TOPSIS
├── includes/
│   ├── header.php         # Template header
│   └── footer.php         # Template footer
├── database.sql           # Schema dan data default
├── index.php             # Halaman utama (redirect)
├── login.php             # Halaman login
├── logout.php            # Proses logout
├── dashboard.php         # Dashboard utama
├── users.php             # Manajemen user
├── kriteria.php          # Manajemen kriteria
├── alternatif.php        # Manajemen alternatif
├── perbandingan.php      # Perbandingan AHP
├── penilaian.php         # Penilaian alternatif
├── ranking.php           # Hasil ranking TOPSIS
├── laporan.php           # Laporan cetak
├── mobile-info.php       # Informasi mobile & PWA
├── assets/
│   ├── css/
│   │   └── mobile.css    # Styles khusus mobile
│   ├── js/
│   │   ├── offline.js    # Offline functionality
│   │   └── notifications.js # Push notifications
│   └── icons/            # PWA icons
├── manifest.json         # PWA manifest
├── sw.js                 # Service worker
└── README.md             # Dokumentasi
```

## Teknologi yang Digunakan

- **Backend**: PHP 7.4+ dengan PDO
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript
- **Framework CSS**: Bootstrap 5.3.0
- **Icons**: Font Awesome 6.0.0
- **DataTables**: jQuery DataTables 1.13.6
- **Alerts**: SweetAlert2
- **PWA**: Service Worker, Web App Manifest
- **Offline**: IndexedDB, Cache API
- **Mobile**: Responsive Design, Touch Gestures

## Keamanan

- Password di-hash menggunakan `password_hash()` PHP
- Prepared statements untuk mencegah SQL injection
- Input sanitization dan validation
- Session management yang aman
- CSRF protection (basic)

## Kontribusi

Untuk berkontribusi pada project ini:

1. Fork repository
2. Buat branch fitur baru
3. Commit perubahan
4. Push ke branch
5. Buat Pull Request

## Lisensi

Project ini dibuat untuk keperluan edukasi dan penelitian.

## Fitur Mobile & PWA

### Progressive Web App (PWA)

- **Installable**: Dapat diinstall sebagai aplikasi native
- **Offline Support**: Berfungsi tanpa koneksi internet
- **Fast Loading**: Caching untuk performa optimal
- **Responsive**: Optimized untuk semua ukuran layar

### Mobile Features

- **Touch Gestures**: Swipe navigation untuk kemudahan penggunaan
- **Mobile-First Design**: Interface yang dioptimalkan untuk mobile
- **Adaptive Layout**: Layout yang menyesuaikan dengan ukuran layar
- **Touch-Friendly**: Button dan form yang mudah disentuh

### Offline Functionality

- **Local Storage**: Data disimpan secara lokal menggunakan IndexedDB
- **Background Sync**: Sinkronisasi otomatis saat koneksi kembali
- **Offline Indicators**: Status koneksi yang jelas
- **Data Persistence**: Data tetap tersimpan saat offline

### Push Notifications

- **Real-time Updates**: Notifikasi untuk update penting
- **Calculation Alerts**: Pemberitahuan saat perhitungan selesai
- **Sync Notifications**: Info saat data berhasil disinkronisasi
- **Offline Alerts**: Pemberitahuan perubahan status koneksi

### Browser Support

- **Chrome/Edge**: Full PWA support
- **Firefox**: Partial PWA support
- **Safari**: iOS PWA support
- **Mobile Browsers**: Optimized untuk semua browser mobile

## Support

Jika mengalami masalah atau memiliki pertanyaan, silakan buat issue di repository ini.
