// Push Notifications for SPK Pupuk Organik

class NotificationManager {
    constructor() {
        this.permission = 'default';
        this.registration = null;
        this.init();
    }

    async init() {
        // Check if notifications are supported
        if (!('Notification' in window)) {
            console.log('This browser does not support notifications');
            return;
        }

        // Check if service worker is supported
        if (!('serviceWorker' in navigator)) {
            console.log('This browser does not support service workers');
            return;
        }

        this.permission = Notification.permission;
        
        // Get service worker registration
        try {
            this.registration = await navigator.serviceWorker.ready;
        } catch (error) {
            console.error('Service worker not ready:', error);
        }
    }

    async requestPermission() {
        if (this.permission === 'granted') {
            return true;
        }

        if (this.permission === 'denied') {
            this.showPermissionDeniedMessage();
            return false;
        }

        try {
            const permission = await Notification.requestPermission();
            this.permission = permission;
            
            if (permission === 'granted') {
                this.showPermissionGrantedMessage();
                return true;
            } else {
                this.showPermissionDeniedMessage();
                return false;
            }
        } catch (error) {
            console.error('Error requesting notification permission:', error);
            return false;
        }
    }

    async showNotification(title, options = {}) {
        if (this.permission !== 'granted') {
            console.log('Notification permission not granted');
            return;
        }

        const defaultOptions = {
            body: 'Notifikasi dari SPK Pupuk Organik',
            icon: '/assets/icons/icon-192x192.png',
            badge: '/assets/icons/icon-72x72.png',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: 1
            },
            actions: [
                {
                    action: 'open',
                    title: 'Buka',
                    icon: '/assets/icons/icon-96x96.png'
                },
                {
                    action: 'close',
                    title: 'Tutup',
                    icon: '/assets/icons/icon-96x96.png'
                }
            ],
            requireInteraction: false,
            silent: false
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            if (this.registration) {
                // Use service worker for persistent notifications
                await this.registration.showNotification(title, finalOptions);
            } else {
                // Fallback to regular notification
                new Notification(title, finalOptions);
            }
        } catch (error) {
            console.error('Error showing notification:', error);
        }
    }

    // Predefined notification types
    async notifyDataSaved(type = 'data') {
        await this.showNotification('Data Tersimpan', {
            body: `${type} berhasil disimpan`,
            icon: '/assets/icons/icon-192x192.png',
            tag: 'data-saved'
        });
    }

    async notifyCalculationComplete() {
        await this.showNotification('Perhitungan Selesai', {
            body: 'Ranking TOPSIS telah dihitung',
            icon: '/assets/icons/icon-192x192.png',
            tag: 'calculation-complete',
            requireInteraction: true
        });
    }

    async notifyOfflineMode() {
        await this.showNotification('Mode Offline', {
            body: 'Aplikasi beralih ke mode offline',
            icon: '/assets/icons/icon-192x192.png',
            tag: 'offline-mode'
        });
    }

    async notifyOnlineMode() {
        await this.showNotification('Kembali Online', {
            body: 'Koneksi tersedia, data akan disinkronisasi',
            icon: '/assets/icons/icon-192x192.png',
            tag: 'online-mode'
        });
    }

    async notifySyncComplete(count) {
        await this.showNotification('Sinkronisasi Selesai', {
            body: `${count} data berhasil disinkronisasi`,
            icon: '/assets/icons/icon-192x192.png',
            tag: 'sync-complete'
        });
    }

    // Schedule notifications
    async scheduleNotification(title, options, delay) {
        setTimeout(async () => {
            await this.showNotification(title, options);
        }, delay);
    }

    // Clear notifications by tag
    async clearNotifications(tag) {
        if (this.registration) {
            try {
                const notifications = await this.registration.getNotifications({ tag });
                notifications.forEach(notification => notification.close());
            } catch (error) {
                console.error('Error clearing notifications:', error);
            }
        }
    }

    // Show permission messages
    showPermissionGrantedMessage() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Notifikasi Diaktifkan',
                text: 'Anda akan menerima notifikasi untuk update penting',
                icon: 'success',
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }
    }

    showPermissionDeniedMessage() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Notifikasi Diblokir',
                text: 'Aktifkan notifikasi di pengaturan browser untuk mendapatkan update',
                icon: 'warning',
                timer: 5000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }
    }

    // Check if notifications are enabled
    isEnabled() {
        return this.permission === 'granted';
    }

    // Get notification settings
    getSettings() {
        return {
            permission: this.permission,
            supported: 'Notification' in window,
            serviceWorkerSupported: 'serviceWorker' in navigator,
            registrationReady: !!this.registration
        };
    }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.notificationManager = new NotificationManager();
    
    // Auto-request permission on first visit (mobile-friendly)
    if (window.innerWidth <= 768 && Notification.permission === 'default') {
        // Show a user-friendly prompt first
        setTimeout(() => {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'Aktifkan Notifikasi?',
                    text: 'Dapatkan notifikasi untuk update penting aplikasi',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Aktifkan',
                    cancelButtonText: 'Tidak',
                    confirmButtonColor: '#28a745'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.notificationManager.requestPermission();
                    }
                });
            }
        }, 3000); // Wait 3 seconds after page load
    }
});

// Listen for online/offline events
window.addEventListener('online', () => {
    if (window.notificationManager && window.notificationManager.isEnabled()) {
        window.notificationManager.notifyOnlineMode();
    }
});

window.addEventListener('offline', () => {
    if (window.notificationManager && window.notificationManager.isEnabled()) {
        window.notificationManager.notifyOfflineMode();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}
