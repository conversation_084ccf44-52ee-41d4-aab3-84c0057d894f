<?php
require_once 'config/config.php';
require_once 'classes/AHP.php';
requireLogin();

$page_title = 'Perbandingan Kriteria (AHP)';
$success = '';
$error = '';

$db = getDB();
$ahp = new AHP($db);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'save_comparison') {
            $kriteria1_id = (int)$_POST['kriteria1_id'];
            $kriteria2_id = (int)$_POST['kriteria2_id'];
            $nilai = (float)$_POST['nilai'];
            
            if ($ahp->saveComparison($kriteria1_id, $kriteria2_id, $nilai)) {
                $success = 'Perbandingan berhasil disimpan!';
            } else {
                $error = 'Gagal menyimpan perbandingan!';
            }
        }
        
        elseif ($_POST['action'] === 'calculate_weights') {
            $result = $ahp->calculateWeights();
            if (!empty($result)) {
                $success = 'Bobot kriteria berhasil dihitung!';
            } else {
                $error = 'Gagal menghitung bobot kriteria!';
            }
        }
        
        elseif ($_POST['action'] === 'reset') {
            if ($ahp->resetComparisons()) {
                $success = 'Semua perbandingan berhasil direset!';
            } else {
                $error = 'Gagal mereset perbandingan!';
            }
        }
    }
}

// Ambil data untuk tampilan
$kriteria = [];
try {
    $stmt = $db->query("SELECT * FROM kriteria ORDER BY kode");
    $kriteria = $stmt->fetchAll();
} catch (Exception $e) {
    $error = 'Terjadi kesalahan dalam mengambil data kriteria.';
}

$matrixData = $ahp->createPairwiseMatrix();
$weightsData = $ahp->calculateWeights();
$isComplete = $ahp->isComparisonComplete();

include 'includes/header.php';
?>

<?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?= $success ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i><?= $error ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (count($kriteria) < 2): ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        Minimal 2 kriteria diperlukan untuk melakukan perbandingan. 
        <a href="kriteria.php" class="alert-link">Tambah kriteria</a> terlebih dahulu.
    </div>
<?php else: ?>

<!-- Perbandingan Berpasangan -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-balance-scale me-2"></i>Perbandingan Berpasangan Kriteria</h5>
        <div>
            <button class="btn btn-success btn-sm me-2" onclick="calculateWeights()">
                <i class="fas fa-calculator me-1"></i>Hitung Bobot
            </button>
            <button class="btn btn-danger btn-sm" onclick="resetComparisons()">
                <i class="fas fa-redo me-1"></i>Reset
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <?php
            $pairs = [];
            for ($i = 0; $i < count($kriteria); $i++) {
                for ($j = $i + 1; $j < count($kriteria); $j++) {
                    $pairs[] = [$kriteria[$i], $kriteria[$j]];
                }
            }
            ?>
            
            <?php foreach ($pairs as $index => $pair): ?>
                <?php
                $k1 = $pair[0];
                $k2 = $pair[1];
                
                // Ambil nilai perbandingan yang sudah ada
                $stmt = $db->prepare("SELECT nilai FROM perbandingan_kriteria WHERE kriteria1_id = ? AND kriteria2_id = ?");
                $stmt->execute([$k1['id'], $k2['id']]);
                $currentValue = $stmt->fetchColumn();
                ?>
                
                <div class="col-12 col-md-6 mb-3">
                    <div class="card border-light">
                        <div class="card-body">
                            <h6 class="card-title">
                                <span class="badge bg-primary"><?= $k1['kode'] ?></span> vs
                                <span class="badge bg-success"><?= $k2['kode'] ?></span>
                            </h6>
                            <p class="card-text small">
                                <span class="d-none d-md-block"><?= htmlspecialchars($k1['nama']) ?> dibandingkan dengan <?= htmlspecialchars($k2['nama']) ?></span>
                                <span class="d-md-none"><?= substr(htmlspecialchars($k1['nama']), 0, 15) ?>... vs <?= substr(htmlspecialchars($k2['nama']), 0, 15) ?>...</span>
                            </p>

                            <form method="POST" class="comparison-form">
                                <input type="hidden" name="action" value="save_comparison">
                                <input type="hidden" name="kriteria1_id" value="<?= $k1['id'] ?>">
                                <input type="hidden" name="kriteria2_id" value="<?= $k2['id'] ?>">

                                <div class="row align-items-center">
                                    <div class="col-9 col-md-8">
                                        <select class="form-select form-select-sm" name="nilai" onchange="this.form.submit()" style="font-size: 14px;">
                                            <option value="">Pilih tingkat kepentingan</option>
                                            <?php foreach ($ahp->getSkalaPerbandingan() as $nilai => $label): ?>
                                                <option value="<?= $nilai ?>" <?= $currentValue == $nilai ? 'selected' : '' ?>>
                                                    <span class="d-none d-md-inline"><?= $nilai ?> - <?= $label ?></span>
                                                    <span class="d-md-none"><?= $nilai ?> - <?= substr($label, 0, 15) ?><?= strlen($label) > 15 ? '...' : '' ?></span>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-3 col-md-4 text-end">
                                        <?php if ($currentValue): ?>
                                            <span class="badge bg-success">✓</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">-</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="mt-3">
            <div class="progress">
                <div class="progress-bar" style="width: <?= count($pairs) > 0 ? (count(array_filter($pairs, function($pair) use ($db) {
                    $stmt = $db->prepare("SELECT COUNT(*) FROM perbandingan_kriteria WHERE kriteria1_id = ? AND kriteria2_id = ?");
                    $stmt->execute([$pair[0]['id'], $pair[1]['id']]);
                    return $stmt->fetchColumn() > 0;
                })) / count($pairs)) * 100 : 0 ?>%">
                </div>
            </div>
            <small class="text-muted">
                Progress: <?= count(array_filter($pairs, function($pair) use ($db) {
                    $stmt = $db->prepare("SELECT COUNT(*) FROM perbandingan_kriteria WHERE kriteria1_id = ? AND kriteria2_id = ?");
                    $stmt->execute([$pair[0]['id'], $pair[1]['id']]);
                    return $stmt->fetchColumn() > 0;
                })) ?> dari <?= count($pairs) ?> perbandingan
            </small>
        </div>
    </div>
</div>

<!-- Matriks Perbandingan -->
<?php if (!empty($matrixData['matrix'])): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Matriks Perbandingan Berpasangan</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>Kriteria</th>
                        <?php foreach ($matrixData['kriteria'] as $k): ?>
                            <th class="text-center"><?= $k['kode'] ?></th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($matrixData['kriteria'] as $i => $k1): ?>
                    <tr>
                        <td><strong><?= $k1['kode'] ?></strong></td>
                        <?php foreach ($matrixData['kriteria'] as $j => $k2): ?>
                            <td class="text-center">
                                <?= formatNumber($matrixData['matrix'][$i][$j], 3) ?>
                            </td>
                        <?php endforeach; ?>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Hasil Bobot -->
<?php if (!empty($weightsData['weights'])): ?>
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Hasil Bobot Kriteria</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Kode</th>
                                <th>Nama Kriteria</th>
                                <th>Bobot</th>
                                <th>Persentase</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($weightsData['kriteria'] as $i => $k): ?>
                            <tr>
                                <td><span class="badge bg-primary"><?= $k['kode'] ?></span></td>
                                <td><?= htmlspecialchars($k['nama']) ?></td>
                                <td><?= formatNumber($weightsData['weights'][$i], 4) ?></td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar" style="width: <?= $weightsData['weights'][$i] * 100 ?>%">
                                            <?= formatNumber($weightsData['weights'][$i] * 100, 1) ?>%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h6>Consistency Ratio (CR)</h6>
                        <h3 class="<?= $weightsData['cr'] <= 0.1 ? 'text-success' : 'text-danger' ?>">
                            <?= formatNumber($weightsData['cr'], 4) ?>
                        </h3>
                        <small class="text-muted">
                            <?php if ($weightsData['cr'] <= 0.1): ?>
                                <i class="fas fa-check-circle text-success"></i> Konsisten
                            <?php else: ?>
                                <i class="fas fa-exclamation-triangle text-danger"></i> Tidak Konsisten
                            <?php endif; ?>
                        </small>
                        <hr>
                        <small>
                            CR ≤ 0.1 = Konsisten<br>
                            CR > 0.1 = Perlu revisi
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php endif; ?>

<?php
$extra_js = "
<script>
function calculateWeights() {
    if (confirm('Hitung ulang bobot kriteria berdasarkan perbandingan yang sudah diinput?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"calculate_weights\">';
        document.body.appendChild(form);
        form.submit();
    }
}

function resetComparisons() {
    if (confirm('Reset semua perbandingan? Data yang sudah diinput akan hilang!')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"reset\">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
";

include 'includes/footer.php';
?>
