<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="description" content="Sistem Pendukung Keputusan Pemilihan Pupuk Organik untuk Rumput Gajah Mini menggunakan metode AHP dan TOPSIS">
    <meta name="keywords" content="SPK, Pupuk Organik, AHP, TOPSIS, Rumput Gajah Mini">
    <meta name="author" content="SPK Pupuk Organik">
    <meta name="theme-color" content="#28a745">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SPK Pupuk">
    <meta name="mobile-web-app-capable" content="yes">
    <title><?= isset($page_title) ? $page_title . ' - ' : '' ?>SPK Pupuk Organik</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="<?= BASE_URL ?>manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="72x72" href="<?= BASE_URL ?>assets/icons/icon-72x72.png">
    <link rel="apple-touch-icon" sizes="96x96" href="<?= BASE_URL ?>assets/icons/icon-96x96.png">
    <link rel="apple-touch-icon" sizes="128x128" href="<?= BASE_URL ?>assets/icons/icon-128x128.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= BASE_URL ?>assets/icons/icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= BASE_URL ?>assets/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="192x192" href="<?= BASE_URL ?>assets/icons/icon-192x192.png">

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="<?= BASE_URL ?>assets/icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="<?= BASE_URL ?>assets/icons/icon-16x16.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="<?= BASE_URL ?>assets/css/mobile.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #20c997;
            --sidebar-width: 250px;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            z-index: 1000;
            transition: all 0.3s;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-header h4 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar-menu {
            padding: 1rem 0;
        }
        
        .sidebar-menu .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border: none;
            transition: all 0.3s;
        }
        
        .sidebar-menu .nav-link:hover,
        .sidebar-menu .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            border-left: 4px solid white;
        }
        
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }
        
        .top-navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 2rem;
            margin-bottom: 2rem;
        }
        
        .content-wrapper {
            padding: 0 2rem 2rem;
        }
        
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 10px 10px 0 0 !important;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .stats-card .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1050;
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .top-navbar {
                padding: 0.75rem 1rem;
            }

            .content-wrapper {
                padding: 0 1rem 1rem;
            }

            .card {
                margin-bottom: 1rem;
            }

            .table-responsive {
                font-size: 0.875rem;
            }

            .btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
            }

            .stats-card {
                padding: 1rem;
                margin-bottom: 0.75rem;
            }

            .stats-card .stats-number {
                font-size: 1.5rem;
            }

            .modal-dialog {
                margin: 0.5rem;
            }

            .form-control, .form-select {
                font-size: 16px; /* Prevent zoom on iOS */
            }
        }

        @media (max-width: 576px) {
            .sidebar-header h4 {
                font-size: 1rem;
            }

            .sidebar-menu .nav-link {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }

            .card-header h5 {
                font-size: 1rem;
            }

            .btn-group-vertical .btn {
                margin-bottom: 0.25rem;
            }

            .table th, .table td {
                padding: 0.5rem 0.25rem;
                font-size: 0.8rem;
            }

            .progress {
                height: 15px;
            }
        }

        /* Mobile overlay */
        .mobile-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1040;
        }

        .mobile-overlay.show {
            display: block;
        }

        /* Touch-friendly buttons */
        .btn-mobile {
            min-height: 44px;
            min-width: 44px;
        }

        /* Swipe indicator */
        .swipe-indicator {
            display: none;
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            z-index: 1060;
        }

        @media (max-width: 768px) {
            .swipe-indicator {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-seedling fa-2x mb-2" style="color: white;"></i>
            <h4>SPK Pupuk Organik</h4>
        </div>
        
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : '' ?>" 
                       href="<?= BASE_URL ?>dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                
                <?php if (isAdmin()): ?>
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : '' ?>" 
                       href="<?= BASE_URL ?>users.php">
                        <i class="fas fa-users me-2"></i>Manajemen User
                    </a>
                </li>
                <?php endif; ?>
                
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'kriteria.php' ? 'active' : '' ?>" 
                       href="<?= BASE_URL ?>kriteria.php">
                        <i class="fas fa-list me-2"></i>Data Kriteria
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'alternatif.php' ? 'active' : '' ?>" 
                       href="<?= BASE_URL ?>alternatif.php">
                        <i class="fas fa-boxes me-2"></i>Data Alternatif
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'perbandingan.php' ? 'active' : '' ?>" 
                       href="<?= BASE_URL ?>perbandingan.php">
                        <i class="fas fa-balance-scale me-2"></i>Perbandingan AHP
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'penilaian.php' ? 'active' : '' ?>" 
                       href="<?= BASE_URL ?>penilaian.php">
                        <i class="fas fa-star me-2"></i>Penilaian Alternatif
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'ranking.php' ? 'active' : '' ?>" 
                       href="<?= BASE_URL ?>ranking.php">
                        <i class="fas fa-trophy me-2"></i>Hasil Ranking
                    </a>
                </li>
                
                <li class="nav-item d-md-none">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'mobile-info.php' ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>mobile-info.php">
                        <i class="fas fa-mobile-alt me-2"></i>Info Mobile
                    </a>
                </li>

                <li class="nav-item mt-3">
                    <a class="nav-link" href="<?= BASE_URL ?>logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </li>
            </ul>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <button class="btn btn-outline-primary d-md-none me-2 btn-mobile" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h5 class="mb-0 d-none d-md-block"><?= isset($page_title) ? $page_title : 'Dashboard' ?></h5>
                <h6 class="mb-0 d-md-none"><?= isset($page_title) ? $page_title : 'Dashboard' ?></h6>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-2 d-none d-sm-block">Selamat datang, <strong><?= $_SESSION['nama_lengkap'] ?></strong></span>
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle btn-mobile" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1 d-none d-sm-inline"></i>
                        <span class="d-none d-sm-inline"><?= $_SESSION['username'] ?></span>
                        <span class="d-sm-none"><i class="fas fa-user"></i></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li class="d-sm-none">
                            <span class="dropdown-item-text">
                                <strong><?= $_SESSION['nama_lengkap'] ?></strong><br>
                                <small class="text-muted"><?= $_SESSION['username'] ?></small>
                            </span>
                        </li>
                        <li class="d-sm-none"><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?= BASE_URL ?>logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Mobile Overlay -->
        <div class="mobile-overlay" id="mobileOverlay"></div>

        <!-- Swipe Indicator -->
        <div class="swipe-indicator" id="swipeIndicator">
            <i class="fas fa-hand-point-left me-2"></i>Geser dari kiri untuk menu
        </div>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
