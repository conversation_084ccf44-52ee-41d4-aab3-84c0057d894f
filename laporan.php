<?php
require_once 'config/config.php';
require_once 'classes/TOPSIS.php';
requireLogin();

$db = getDB();
$topsis = new TOPSIS($db);

// Ambil data untuk laporan
$latestRanking = $topsis->getLatestRanking();
$topsisResult = $topsis->calculateTOPSIS();

// Ambil data kriteria dan alternatif
try {
    $stmt = $db->query("SELECT * FROM kriteria ORDER BY kode");
    $kriteria = $stmt->fetchAll();
    
    $stmt = $db->query("SELECT * FROM alternatif ORDER BY kode");
    $alternatif = $stmt->fetchAll();
} catch (Exception $e) {
    $kriteria = [];
    $alternatif = [];
}

$print_mode = isset($_GET['print']) && $_GET['print'] == '1';
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laporan Hasil SPK Pupuk Organik</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .page-break { page-break-before: always; }
            body { font-size: 12px; }
            .table { font-size: 11px; }
        }
        
        .header-logo {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #28a745;
            padding-bottom: 20px;
        }
        
        .signature-section {
            margin-top: 50px;
        }
        
        .signature-box {
            text-align: center;
            margin-top: 80px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="header-logo">
            <h2 class="text-success mb-1">SISTEM PENDUKUNG KEPUTUSAN</h2>
            <h3 class="mb-1">PEMILIHAN KOMPOSISI PUPUK ORGANIK</h3>
            <h4 class="mb-1">PADA RUMPUT GAJAH MINI</h4>
            <p class="mb-0">Menggunakan Metode AHP dan TOPSIS</p>
            <small class="text-muted">Tanggal Laporan: <?= date('d F Y') ?></small>
        </div>

        <!-- Tombol Print -->
        <?php if (!$print_mode): ?>
        <div class="no-print mb-4">
            <div class="d-flex justify-content-between">
                <a href="ranking.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Kembali
                </a>
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print me-2"></i>Cetak Laporan
                </button>
            </div>
        </div>
        <?php endif; ?>

        <!-- 1. Pendahuluan -->
        <section class="mb-5">
            <h4 class="text-success border-bottom pb-2">1. PENDAHULUAN</h4>
            <p class="text-justify">
                Sistem Pendukung Keputusan (SPK) ini dikembangkan untuk membantu dalam pemilihan komposisi pupuk organik 
                yang optimal untuk rumput gajah mini. Sistem ini menggunakan metode Analytical Hierarchy Process (AHP) 
                untuk menentukan bobot kriteria dan metode TOPSIS (Technique for Order Preference by Similarity to Ideal Solution) 
                untuk melakukan perangkingan alternatif pupuk organik.
            </p>
        </section>

        <!-- 2. Data Kriteria -->
        <section class="mb-5">
            <h4 class="text-success border-bottom pb-2">2. KRITERIA PENILAIAN</h4>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-success">
                        <tr>
                            <th>No</th>
                            <th>Kode</th>
                            <th>Nama Kriteria</th>
                            <th>Jenis</th>
                            <th>Bobot</th>
                            <th>Deskripsi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($kriteria as $index => $k): ?>
                        <tr>
                            <td><?= $index + 1 ?></td>
                            <td><?= $k['kode'] ?></td>
                            <td><?= htmlspecialchars($k['nama']) ?></td>
                            <td><?= ucfirst($k['jenis']) ?></td>
                            <td><?= formatNumber($k['bobot'], 4) ?></td>
                            <td><?= htmlspecialchars($k['deskripsi']) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- 3. Data Alternatif -->
        <section class="mb-5">
            <h4 class="text-success border-bottom pb-2">3. ALTERNATIF PUPUK ORGANIK</h4>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-success">
                        <tr>
                            <th>No</th>
                            <th>Kode</th>
                            <th>Nama Alternatif</th>
                            <th>Deskripsi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($alternatif as $index => $alt): ?>
                        <tr>
                            <td><?= $index + 1 ?></td>
                            <td><?= $alt['kode'] ?></td>
                            <td><?= htmlspecialchars($alt['nama']) ?></td>
                            <td><?= htmlspecialchars($alt['deskripsi']) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Page Break -->
        <div class="page-break"></div>

        <!-- 4. Hasil Perhitungan AHP -->
        <section class="mb-5">
            <h4 class="text-success border-bottom pb-2">4. HASIL PERHITUNGAN BOBOT KRITERIA (AHP)</h4>
            <p>Bobot kriteria dihitung menggunakan metode Analytical Hierarchy Process (AHP) berdasarkan perbandingan berpasangan antar kriteria.</p>
            
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-success">
                        <tr>
                            <th>Kriteria</th>
                            <th>Bobot</th>
                            <th>Persentase</th>
                            <th>Prioritas</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $sortedKriteria = $kriteria;
                        usort($sortedKriteria, function($a, $b) {
                            return $b['bobot'] <=> $a['bobot'];
                        });
                        ?>
                        <?php foreach ($sortedKriteria as $index => $k): ?>
                        <tr>
                            <td><?= $k['kode'] ?> - <?= htmlspecialchars($k['nama']) ?></td>
                            <td><?= formatNumber($k['bobot'], 4) ?></td>
                            <td><?= formatNumber($k['bobot'] * 100, 2) ?>%</td>
                            <td><?= $index + 1 ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- 5. Hasil Ranking TOPSIS -->
        <?php if (!empty($latestRanking)): ?>
        <section class="mb-5">
            <h4 class="text-success border-bottom pb-2">5. HASIL RANKING TOPSIS</h4>
            <p>Ranking alternatif pupuk organik berdasarkan perhitungan metode TOPSIS:</p>
            
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-success">
                        <tr>
                            <th>Ranking</th>
                            <th>Kode</th>
                            <th>Nama Alternatif</th>
                            <th>Skor TOPSIS</th>
                            <th>Persentase</th>
                            <th>Kategori</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($latestRanking as $rank): ?>
                        <tr class="<?= $rank['ranking'] == 1 ? 'table-warning' : '' ?>">
                            <td class="text-center">
                                <strong><?= $rank['ranking'] ?></strong>
                                <?php if ($rank['ranking'] == 1): ?>
                                    <span class="badge bg-warning ms-1">TERBAIK</span>
                                <?php endif; ?>
                            </td>
                            <td><?= $rank['kode'] ?></td>
                            <td><?= htmlspecialchars($rank['nama']) ?></td>
                            <td><?= formatNumber($rank['skor_topsis'], 6) ?></td>
                            <td><?= formatNumber($rank['skor_topsis'] * 100, 2) ?>%</td>
                            <td>
                                <?php if ($rank['ranking'] == 1): ?>
                                    Sangat Direkomendasikan
                                <?php elseif ($rank['ranking'] <= 2): ?>
                                    Direkomendasikan
                                <?php else: ?>
                                    Cukup Direkomendasikan
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </section>
        <?php endif; ?>

        <!-- 6. Kesimpulan -->
        <section class="mb-5">
            <h4 class="text-success border-bottom pb-2">6. KESIMPULAN DAN REKOMENDASI</h4>
            <?php if (!empty($latestRanking)): ?>
                <?php $terbaik = $latestRanking[0]; ?>
                <p class="text-justify">
                    Berdasarkan hasil perhitungan menggunakan metode AHP dan TOPSIS, alternatif pupuk organik yang 
                    <strong>paling direkomendasikan</strong> untuk rumput gajah mini adalah 
                    <strong><?= htmlspecialchars($terbaik['nama']) ?> (<?= $terbaik['kode'] ?>)</strong> 
                    dengan skor TOPSIS sebesar <strong><?= formatNumber($terbaik['skor_topsis'], 4) ?></strong> 
                    atau <strong><?= formatNumber($terbaik['skor_topsis'] * 100, 2) ?>%</strong>.
                </p>
                
                <p class="text-justify">
                    Rekomendasi ini didasarkan pada evaluasi terhadap <?= count($kriteria) ?> kriteria penilaian 
                    yang meliputi: <?= implode(', ', array_map(function($k) { return $k['nama']; }, $kriteria)) ?>. 
                    Hasil ini dapat dijadikan acuan dalam pengambilan keputusan pemilihan pupuk organik 
                    yang optimal untuk meningkatkan produktivitas rumput gajah mini.
                </p>
            <?php else: ?>
                <p class="text-justify">
                    Belum ada hasil perhitungan yang tersedia. Silakan lengkapi data kriteria, alternatif, 
                    dan penilaian terlebih dahulu untuk mendapatkan rekomendasi.
                </p>
            <?php endif; ?>
        </section>

        <!-- Tanda Tangan -->
        <div class="signature-section">
            <div class="row">
                <div class="col-md-6"></div>
                <div class="col-md-6">
                    <div class="signature-box">
                        <p>Jakarta, <?= date('d F Y') ?></p>
                        <p>Mengetahui,</p>
                        <div style="height: 80px;"></div>
                        <p><strong>(_____________________)</strong></p>
                        <p>Penanggung Jawab</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
